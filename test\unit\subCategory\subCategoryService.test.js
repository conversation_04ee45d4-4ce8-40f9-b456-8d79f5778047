/* eslint-disable no-undef */
const SubCategoryService = require('../../../src/api/v1/subCategory/subCategoryService');
const SubCategory = require('../../../src/api/v1/subCategory/subCategoryModel');
const Counter = require('../../../src/api/v1/subCategory/subCategoryCounter');

jest.mock('../../../src/api/v1/subCategory/subCategoryModel');
jest.mock('../../../src/api/v1/subCategory/subCategoryCounter');

describe('SubCategory Service', () => {
    let mockSubCategoryData;

    beforeEach(() => {
        // Initialize mock data for each test
        mockSubCategoryData = {
            subCategoryName: 'Test SubCategory',
            subCategorySlug: 'test-subcategory',
        };
        jest.clearAllMocks(); // Clear previous mocks to isolate tests
        SubCategory.prototype.save = jest.fn();
    });

    describe('createSubCategory', () => {
        test('should create a new subcategory with an alternative approach', async () => {
            // Arrange
            const mockId = 2;
            const subCategoryId = `SCID_${mockId}`;
            const mockSubCategory = {
                subCategoryName: 'Test SubCategory',
                subCategorySlug: 'test-subcategory',
                subCategoryId,
            };

            // Mock the behavior of getNextSequence and save
            Counter.getNextSequence.mockResolvedValue(mockId); // Simulate Counter returning 2

            // Mock save to return the mockSubCategory value
            SubCategory.prototype.save.mockResolvedValue(mockSubCategory);

            // Act
            const result =
                await SubCategoryService.createSubCategory(mockSubCategory);

            // Assert
            // Ensure Counter.getNextSequence was called exactly once
            expect(Counter.getNextSequence).toHaveBeenCalledTimes(1);

            // Use mockImplementationOnce to validate the resolved value of the save function
            expect(SubCategory.prototype.save).toHaveBeenCalled();
            const saveResult =
                await SubCategory.prototype.save.mock.results[0].value;

            // Verify that save returned the correct subCategory
            expect(saveResult).toEqual(mockSubCategory);

            // Verify the result matches the mockSubCategory
            expect(result).toEqual(mockSubCategory);
        });

        test('should throw error if creation fails', async () => {
            // Arrange
            const errorMessage = 'Database error';
            Counter.getNextSequence.mockResolvedValue(1);
            SubCategory.prototype.save.mockRejectedValue(
                new Error(errorMessage)
            );

            // Act & Assert
            await expect(
                SubCategoryService.createSubCategory(mockSubCategoryData)
            ).rejects.toThrow(`Error creating SubCategory: ${errorMessage}`);
        });
    });

    describe('getSubCategoryById', () => {
        test('should return a subcategory by ID', async () => {
            // Arrange
            const subCategoryId = 'SCID_1';
            const subCategory = {
                subCategoryId,
                subCategoryName: 'Test SubCategory',
            };
            SubCategory.findOne.mockResolvedValue(subCategory);

            // Act
            const result =
                await SubCategoryService.getSubCategoryById(subCategoryId);

            // Assert
            expect(SubCategory.findOne).toHaveBeenCalledWith({ subCategoryId });
            expect(result).toEqual(subCategory);
        });

        test('should return null if subcategory not found', async () => {
            // Arrange
            const subCategoryId = 'SCID_1';
            SubCategory.findOne.mockResolvedValue(null);

            // Act
            const result =
                await SubCategoryService.getSubCategoryById(subCategoryId);

            // Assert
            expect(SubCategory.findOne).toHaveBeenCalledWith({ subCategoryId });
            expect(result).toBeNull();
        });
    });

    describe('updateSubCategory', () => {
        test('should update an existing subcategory', async () => {
            // Arrange
            const subCategoryId = 'SCID_1';
            const updateData = { subCategoryName: 'Updated SubCategory' };
            const updatedSubCategory = {
                subCategoryId,
                subCategoryName: 'Updated SubCategory',
            };

            // Mock the methods
            SubCategory.findOne.mockResolvedValue({ subCategoryId }); // Ensure subcategory exists
            SubCategory.findOneAndUpdate.mockResolvedValue(updatedSubCategory); // Mock the update method

            // Act
            const result = await SubCategoryService.updateSubCategory(
                subCategoryId,
                updateData
            );

            // Assert
            expect(SubCategory.findOne).toHaveBeenCalledWith({ subCategoryId });
            expect(SubCategory.findOneAndUpdate).toHaveBeenCalledWith(
                { subCategoryId },
                { $set: updateData },
                { new: true, runValidators: true }
            );
            expect(result).toEqual(updatedSubCategory);
        });

        test('should throw error if subcategory not found', async () => {
            // Arrange
            const subCategoryId = 'SCID_1';
            const updateData = { subCategoryName: 'Updated SubCategory' };

            SubCategory.findOne.mockResolvedValue(null); // SubCategory not found

            // Act & Assert
            await expect(
                SubCategoryService.updateSubCategory(subCategoryId, updateData)
            ).rejects.toThrow('SubCategory not found');
        });
    });

    describe('deleteSubCategory', () => {
        test('should delete an existing subcategory', async () => {
            // Arrange
            const subCategoryId = 'SCID_1';
            const deletedSubCategory = {
                subCategoryId,
                subCategoryName: 'Test SubCategory',
            };

            // Mock methods for subcategory existence and deletion
            SubCategory.findOne.mockResolvedValue({ subCategoryId });
            SubCategory.findOneAndDelete.mockResolvedValue(deletedSubCategory);

            // Act
            const result =
                await SubCategoryService.deleteSubCategory(subCategoryId);

            // Assert
            expect(SubCategory.findOne).toHaveBeenCalledWith({ subCategoryId });
            expect(SubCategory.findOneAndDelete).toHaveBeenCalledWith({
                subCategoryId,
            });
            expect(result).toEqual(deletedSubCategory);
        });

        test('should throw error if subcategory not found', async () => {
            // Arrange
            const subCategoryId = 'SCID_1';
            SubCategory.findOne.mockResolvedValue(null); // SubCategory not found

            // Act & Assert
            await expect(
                SubCategoryService.deleteSubCategory(subCategoryId)
            ).rejects.toThrow('SubCategory not found');
        });
    });
});
