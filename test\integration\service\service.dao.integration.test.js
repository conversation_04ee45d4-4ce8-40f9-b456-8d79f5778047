/* eslint-disable prettier/prettier */
/* eslint-disable no-undef */
const { setupDB, teardownDB } = require('../../environment/setupTest');
const ServiceDAO = require('../../../src/api/v1/serviceInfo/model/serviceInfoModel');

beforeAll(async () => {
    await setupDB();
});

afterAll(async () => {
    await teardownDB();
});

describe('ServiceDAO Integration Tests', () => {
    let serviceId;

    it('should create a new service', async () => {
        const serviceData = {
            serviceTitle: 'Test Service',
            categoryId: 'cat123',
            subCategoryId: 'subcat123',
            price: 50,
            duration: '1 hour',
            description: 'This is a test service description.',
            additionalServicesId: ['serviceItem1', 'serviceItem2'],
            serviceAvailableId: ['availableTimeSlot1', 'availableTimeSlot2'],
            locationId: ['location1', 'location2'],
            galleryId: ['galleryItem1', 'galleryItem2'],
            seoId: ['seoItem1', 'seoItem2'],
        };

        try {
            const createdService = await ServiceDAO.createService(serviceData);

            expect(createdService).toHaveProperty('serviceId');
            serviceId = createdService.serviceId;
        } catch (error) {
            console.error('Error during service creation: ', error);
        }
    });

    it('should fetch the created service by ID', async () => {
        if (!serviceId) {
            console.error('Service ID is not available. Test cannot run.');
            return;
        }

        const service = await ServiceDAO.getServiceById(serviceId);

        expect(service).toHaveProperty('serviceId', serviceId);
        expect(service.serviceTitle).toBe('Test Service');
    });

    it('should update an existing service', async () => {
        if (!serviceId) {
            console.error('Service ID is not available. Test cannot run.');
            return;
        }

        const updatedServiceData = {
            serviceTitle: 'Updated Test Service',
            price: 60,
        };

        const updatedService = await ServiceDAO.updateService(
            serviceId,
            updatedServiceData
        );

        expect(updatedService).toHaveProperty('serviceId', serviceId);
        expect(updatedService.serviceTitle).toBe(
            updatedServiceData.serviceTitle
        );
        expect(updatedService.price).toBe(updatedServiceData.price);
    });

    it('should delete the created service', async () => {
        if (!serviceId) {
            console.error('Service ID is not available. Test cannot run.');
            return;
        }

        const deletionResult = await ServiceDAO.deleteService(serviceId);

        expect(deletionResult).toBe(true);
    });
});
