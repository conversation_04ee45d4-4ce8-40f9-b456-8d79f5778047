const mongoose = require('mongoose');

const reviewCounterSchema = new mongoose.Schema({
    _id: { type: String, required: true },
    sequence_value: { type: Number, default: 0 },
});

const ReviewCounter = mongoose.model('ReviewCounter', reviewCounterSchema);

const getNextSequenceValue = async (sequenceName) => {
    const sequenceDocument = await ReviewCounter.findByIdAndUpdate(
        sequenceName,
        { $inc: { sequence_value: 1 } },
        { new: true, upsert: true }
    );
    return sequenceDocument.sequence_value;
};

module.exports = { getNextSequenceValue, ReviewCounter };
