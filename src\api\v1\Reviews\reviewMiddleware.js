const { body, param, query } = require('express-validator');

const validateReview = [
    body('providerId')
        
        .isString()
        .withMessage('providerId must be a string.')
        .trim(),

    body('serviceId')
        .notEmpty()
        .withMessage('serviceId is required.')
        .isString()
        .withMessage('serviceId must be a string.')
        .trim(),

    body('bookingId')
        .notEmpty()
        .withMessage('bookingId is required.')
        .isString()
        .withMessage('bookingId must be a string.')
        .trim(),

    body('rating')
        .notEmpty()
        .withMessage('rating is required.')
        .isInt({ min: 1, max: 5 })
        .withMessage('rating must be an integer between 1 and 5.'),

    body('title')
        .optional()
        .isString()
        .withMessage('title must be a string.')
        .isLength({ max: 100 })
        .withMessage('title must not exceed 100 characters.')
        .trim(),

    body('comment')
        .notEmpty()
        .withMessage('comment is required.')
        .isString()
        .withMessage('comment must be a string.')
        .isLength({ min: 10, max: 2000 })
        .withMessage('comment must be between 10 and 2000 characters.')
        .trim(),

    body('images')
        .optional()
        .isArray()
        .withMessage('images must be an array.')
        .custom((images) => {
            if (images.length > 5) {
                throw new Error('Maximum 5 images allowed per review.');
            }

            images.forEach((image, index) => {
                if (!image.imageUrl || typeof image.imageUrl !== 'string') {
                    throw new Error(`Image ${index + 1}: imageUrl is required and must be a string.`);
                }
                if (image.imageCaption && typeof image.imageCaption !== 'string') {
                    throw new Error(`Image ${index + 1}: imageCaption must be a string.`);
                }
                if (image.imageCaption && image.imageCaption.length > 200) {
                    throw new Error(`Image ${index + 1}: imageCaption must not exceed 200 characters.`);
                }
            });

            return true;
        }),
];

const validateReviewUpdate = [
    param('reviewId')
        .notEmpty()
        .withMessage('reviewId is required.')
        .isString()
        .withMessage('reviewId must be a string.')
        .trim(),

    body('rating')
        .optional()
        .isInt({ min: 1, max: 5 })
        .withMessage('rating must be an integer between 1 and 5.'),

    body('title')
        .optional()
        .isString()
        .withMessage('title must be a string.')
        .isLength({ max: 100 })
        .withMessage('title must not exceed 100 characters.')
        .trim(),

    body('comment')
        .optional()
        .isString()
        .withMessage('comment must be a string.')
        .isLength({ min: 10, max: 2000 })
        .withMessage('comment must be between 10 and 2000 characters.')
        .trim(),

    body('images')
        .optional()
        .isArray()
        .withMessage('images must be an array.')
        .custom((images) => {
            if (images && images.length > 5) {
                throw new Error('Maximum 5 images allowed per review.');
            }
            if (images) {
                images.forEach((image, index) => {
                    if (!image.imageUrl || typeof image.imageUrl !== 'string') {
                        throw new Error(`Image ${index + 1}: imageUrl is required and must be a string.`);
                    }
                    if (image.imageCaption && typeof image.imageCaption !== 'string') {
                        throw new Error(`Image ${index + 1}: imageCaption must be a string.`);
                    }
                    if (image.imageCaption && image.imageCaption.length > 200) {
                        throw new Error(`Image ${index + 1}: imageCaption must not exceed 200 characters.`);
                    }
                });
            }
            return true;
        }),
];

const validateProviderResponse = [
    param('reviewId')
        .notEmpty()
        .withMessage('reviewId is required.')
        .isString()
        .withMessage('reviewId must be a string.')
        .trim(),

    body('responseText')
        .notEmpty()
        .withMessage('responseText is required.')
        .isString()
        .withMessage('responseText must be a string.')
        .isLength({ min: 10, max: 1000 })
        .withMessage('responseText must be between 10 and 1000 characters.')
        .trim(),
];

const validateHelpfulVote = [
    param('reviewId')
        .notEmpty()
        .withMessage('reviewId is required.')
        .isString()
        .withMessage('reviewId must be a string.')
        .trim(),

    body('voteType')
        .notEmpty()
        .withMessage('voteType is required.')
        .isIn(['helpful', 'not_helpful'])
        .withMessage('voteType must be either "helpful" or "not_helpful".'),
];

const validateReviewModeration = [
    param('reviewId')
        .notEmpty()
        .withMessage('reviewId is required.')
        .isString()
        .withMessage('reviewId must be a string.')
        .trim(),

    body('status')
        .notEmpty()
        .withMessage('status is required.')
        .isIn(['approved', 'rejected', 'hidden'])
        .withMessage('status must be one of: approved, rejected, hidden.'),

    body('moderationReason')
        .optional()
        .isString()
        .withMessage('moderationReason must be a string.')
        .trim(),
];

const validateReviewQuery = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('page must be a positive integer.'),

    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('limit must be an integer between 1 and 100.'),

    query('rating')
        .optional()
        .isInt({ min: 1, max: 5 })
        .withMessage('rating must be an integer between 1 and 5.'),

    query('status')
        .optional()
        .isIn(['pending', 'approved', 'rejected', 'hidden'])
        .withMessage('status must be one of: pending, approved, rejected, hidden.'),

    query('sortBy')
        .optional()
        .isIn(['reviewDate', 'rating', 'helpfulCount', 'updatedAt'])
        .withMessage('sortBy must be one of: reviewDate, rating, helpfulCount, updatedAt.'),

    query('sortOrder')
        .optional()
        .isIn(['asc', 'desc'])
        .withMessage('sortOrder must be either "asc" or "desc".'),
];

const validateRatingQuery = [
    query('serviceId')
        .optional()
        .isString()
        .withMessage('serviceId must be a string.')
        .trim(),

    query('providerId')
        .optional()
        .isString()
        .withMessage('providerId must be a string.')
        .trim(),
];

const validateMultipleRatingQuery = [
    query('serviceIds')
        .optional()
        .custom((value) => {
            if (value) {
                const ids = Array.isArray(value) ? value : value.split(',');
                if (ids.length > 50) {
                    throw new Error('Maximum 50 serviceIds allowed per request.');
                }
                ids.forEach((id, index) => {
                    if (!id || typeof id !== 'string' || id.trim() === '') {
                        throw new Error(`serviceId at index ${index} must be a non-empty string.`);
                    }
                });
            }
            return true;
        }),

    query('providerIds')
        .optional()
        .custom((value) => {
            if (value) {
                const ids = Array.isArray(value) ? value : value.split(',');
                if (ids.length > 50) {
                    throw new Error('Maximum 50 providerIds allowed per request.');
                }
                ids.forEach((id, index) => {
                    if (!id || typeof id !== 'string' || id.trim() === '') {
                        throw new Error(`providerId at index ${index} must be a non-empty string.`);
                    }
                });
            }
            return true;
        }),
];

module.exports = {
    validateReview,
    validateReviewUpdate,
    validateProviderResponse,
    validateHelpfulVote,
    validateReviewModeration,
    validateReviewQuery,
    validateRatingQuery,
    validateMultipleRatingQuery,
};
