const { validationResult } = require('express-validator');

const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const CategoryService = require('./categoryService');
// const featureToggle = require('../../common/utils/FeatureToggle');
const {
    buildQueryFilter,
    validateAndParseParams,
} = require('../../common/utils/filter');

const logger = require('../../common/utils/logger');

const errorUtil = require('../../common/utils/error');

// const createCategory = async (req, res) => {
//     try {
//         const errors = validationResult(req);
//         if (!errors.isEmpty()) {
//             const formattedErrors = errors.array().map((err) => ({
//                 field: err.path,
//                 message: err.msg,
//             }));

//             const errorId = errorUtil.generateErrorId();
//             const errorResponse = errorUtil.createErrorResponse(
//                 formattedErrors,
//                 errorUtil.ERROR_TYPES.VALIDATION_ERROR,
//                 422,
//                 errorId
//             );
//             logger.error(`Validation error for request: ${formattedErrors}`, {
//                 errorId,
//                 formattedErrors,
//             });
//             return res.status(422).json(errorResponse);
//         }

//         const category = await CategoryService.createCategory(req.body);
//         logger.info(
//             `Category created successfully. ID: ${category.categoryId}`
//         );
//         return res.status(201).json({
//             success: true,
//             message: 'Category created successfully',
//             category,
//         });
//     } catch (err) {
//         const errorId = errorUtil.generateErrorId();
//         if (err instanceof InsufficientScopeError) {
//             logger.error(
//                 ` Category Insufficient scope error. Message: ${err.message}`,
//                 {
//                     errorId,
//                 }
//             );
//             const errorResponse = errorUtil.createErrorResponse(
//                 [{ field: 'Category scope', message: err.message }],
//                 errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
//                 403,
//                 errorId
//             );
//             return res.status(403).json(errorResponse);
//         }

//         logger.error(`Error creating category. Message: ${err.message}`, {
//             errorId,
//         });
//         const errorResponse = errorUtil.createErrorResponse(
//             [{ field: 'category', message: err.message }],
//             errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
//             500,
//             errorId
//         );
//         return res.status(500).json(errorResponse);
//     }
// };

const createCategory = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for request: ${JSON.stringify(formattedErrors)}`,
                {
                    errorId,
                    formattedErrors,
                }
            );

            return res.status(422).json(errorResponse);
        }

        const userId = req.userData.user.userId;

        const category = await CategoryService.createCategory(req.body, userId);

        logger.info(
            `Category created successfully. ID: ${category.categoryId}`
        );

        return res.status(201).json({
            success: true,
            message: 'Category created successfully',
            category,
        });
    } catch (err) {
        // Generate a unique error ID for logging and response
        const errorId = errorUtil.generateErrorId();

        // Handle InsufficientScopeError
        if (err instanceof InsufficientScopeError) {
            logger.error(
                `Category Insufficient scope error. Message: ${err.message}`,
                {
                    errorId,
                }
            );

            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'Category scope', message: err.message }],
                errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );

            return res.status(403).json(errorResponse);
        }

        // Handle other errors (Internal Server Error)
        logger.error(`Error creating category. Message: ${err.message}`, {
            errorId,
        });

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'category', message: err.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        return res.status(500).json(errorResponse);
    }
};

const getCategory = async (req, res) => {
    try {
        const {
            page = '1',
            limit = '10',
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
        } = req.query;
        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );
        const query = buildQueryFilter(search);

        const { categories, total } = await CategoryService.getCategories(
            query,
            sortBy,
            sortDirection,
            pageNum,
            limitNum
        );

        const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;
        logger.info(
            `Fetched ${categories.length} categories from page ${pageNum}`
        );
        return res.status(200).json({
            success: true,
            message: 'Categories fetched successfully',
            categories,
            total,
            page: pageNum,
            pages: totalPages,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(`Error fetching categories. Message: ${err.message}`, {
            errorId,
        });
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getCategoryAndSubCategories = async (req, res) => {
    try {
        const { categoriesWithSubCategories } =
            await CategoryService.getCategoriesAndSub();
        logger.info(
            `Fetched ${categoriesWithSubCategories.length} categories from page `
        );
        return res.status(200).json({
            success: true,
            message: 'Categories fetched successfully',
            categoriesWithSubCategories,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(`Error fetching categories. Message: ${err.message}`, {
            errorId,
        });
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getCategoryById = async (req, res) => {
    // const isEnabled = featureToggle.isFeatureEnabled('new-feature-flag');

    // if (isEnabled) {
    //     console.info('🎉 New Feature is Enabled!');
    // } else {
    //     console.info('🚫 This Feature is Currently Disabled.');
    // }

    try {
        const { categoryId } = req.params;
        const category = await CategoryService.getCategoryById(categoryId);

        if (!category) {
            const errorId = errorUtil.generateErrorId(); // Use the new method to generate errorId
            logger.warn(`Category with ID ${categoryId} not found`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'categoryId', message: 'Category not found' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        logger.info(`Fetched category with ID: ${categoryId}`);
        return res.status(200).json({
            success: true,
            message: 'Category fetched successfully',
            category,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId(); // Use the new method to generate errorId
        logger.error(
            `Error fetching category with ID ${req.params.categoryId}. Message: ${err.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const updateCategory = async (req, res) => {
    try {
        const { categoryId } = req.params;
        const existingCategory =
            await CategoryService.getCategoryById(categoryId);

        if (!existingCategory) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(`Category with ID ${categoryId} not found for update`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'categoryId',
                        message: 'Category not found for update',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        const updatedCategory = await CategoryService.updateCategory(
            categoryId,
            req.body
        );
        logger.info(`Category with ID ${categoryId} updated successfully`);
        return res.status(200).json({
            success: true,
            message: 'Category updated successfully',
            category: updatedCategory,
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error updating category with ID ${req.params.categoryId}. Message: ${err.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const deleteCategory = async (req, res) => {
    try {
        const { categoryId } = req.params;
        const category = await CategoryService.getCategoryById(categoryId);

        if (!category) {
            const errorId = errorUtil.generateErrorId();
            logger.warn(
                `Category with ID ${categoryId} not found for deletion`,
                { errorId }
            );
            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'categoryId',
                        message: 'Category not found for deletion',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        await CategoryService.deleteCategory(categoryId);
        logger.info(`Category with ID ${categoryId} deleted successfully`);
        return res.status(200).json({
            success: true,
            message: 'Category deleted successfully',
        });
    } catch (err) {
        const errorId = errorUtil.generateErrorId();
        logger.error(
            `Error deleting category with ID ${req.params.categoryId}. Message: ${err.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

module.exports = {
    createCategory,
    getCategory,
    getCategoryAndSubCategories,
    getCategoryById,
    updateCategory,
    deleteCategory,
};
