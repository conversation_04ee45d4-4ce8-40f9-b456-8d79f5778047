const SubCategory = require('./subCategoryModel');

const Counter = require('./subCategoryCounter');

const logger = require('../../common/utils/logger');

const createSubCategory = async (subCategoryData, createdAt) => {
    try {
        const scid = await Counter.getNextSequence();

        const subCategoryId = `SCID_${scid}`;

        const subCategory = new SubCategory({
            ...subCategoryData,
            subCategoryId,
            createdAt,
        });

        return await subCategory.save();
    } catch (error) {
        throw new Error('Error creating SubCategory: ' + error.message);
    }
};

const getSubCategories = async (
    query,
    sortBy,
    sortDirection,
    pageNum,
    limitNum
) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error(
            'Invalid sort direction. Use 1 for ascending and -1 for descending.'
        );
    }

    try {
        const subCategories = await SubCategory.find(query)
            .sort({ updatedAt: -1, [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum);

        const total = await SubCategory.countDocuments(query);
        return { subCategories, total };
    } catch (error) {
        logger.error('Error fetching subCategories:', error);
        throw new Error('Failed to retrieve subCategories.');
    }
};

const getSubCategoryById = async (subCategoryId) => {
    return SubCategory.findOne({ subCategoryId });
};

const updateSubCategory = async (subCategoryId, updateData) => {
    try {
        if (!subCategoryId) throw new Error('SubCategory ID is required');

        const subCategory = await getSubCategoryById(subCategoryId);

        if (!subCategory) throw new Error('SubCategory not found');

        const updatedSubCategory = await SubCategory.findOneAndUpdate(
            { subCategoryId },
            { $set: updateData },
            { new: true, runValidators: true }
        );

        return updatedSubCategory;
    } catch (error) {
        logger.error('Error updating SubCategory:', error.message);
        throw error;
    }
};

const deleteSubCategory = async (subCategoryId) => {
    try {
        if (!subCategoryId) throw new Error('SubCategory ID is required');

        const subCategory = await getSubCategoryById(subCategoryId);

        if (!subCategory) throw new Error('SubCategory not found');

        const deletedSubCategory = await SubCategory.findOneAndDelete({
            subCategoryId: subCategoryId,
        });

        if (!deletedSubCategory) {
            throw new Error('SubCategory not found');
        }

        return deletedSubCategory;
    } catch (error) {
        throw new Error(error.message);
    }
};

module.exports = {
    createSubCategory,
    getSubCategories,
    getSubCategoryById,
    updateSubCategory,
    deleteSubCategory,
};
