/* eslint-disable no-undef */
const CategoryService = require('../../../src/api/v1/category/categoryService');
const Category = require('../../../src/api/v1/category/categoryModel');
const Counter = require('../../../src/api/v1/category/categoryCounter');

jest.mock('../../../src/api/v1/category/categoryModel');
jest.mock('../../../src/api/v1/category/categoryCounter');

describe('Category Service', () => {
    let mockCategoryData;

    beforeEach(() => {
        // Initialize mock data for each test
        mockCategoryData = {
            categoryName: 'Test Category',
            categorySlug: 'test-category',
        };
        jest.clearAllMocks(); // Clear previous mocks to isolate tests
        Category.prototype.save = jest.fn();
    });

    describe('createCategory', () => {
        test('should create a new category with an alternative approach', async () => {
            // Arrange
            const mockId = 2;
            const categoryId = `CID_${mockId}`;
            const mockCategory = {
                categoryName: 'Test Category',
                categorySlug: 'test-category',
                categoryId,
            };

            // Mock the behavior of getNextSequence and save
            Counter.getNextSequence.mockResolvedValue(mockId); // Simulate Counter returning 2

            // Mock save to return the mockCategory value
            Category.prototype.save.mockResolvedValue(mockCategory);

            // Act
            const result = await CategoryService.createCategory(mockCategory);

            // Assert
            // Ensure Counter.getNextSequence was called exactly once
            expect(Counter.getNextSequence).toHaveBeenCalledTimes(1);

            // Use mockImplementationOnce to validate the resolved value of the save function
            expect(Category.prototype.save).toHaveBeenCalled();
            const saveResult =
                await Category.prototype.save.mock.results[0].value;

            // Verify that save returned the correct category
            expect(saveResult).toEqual(mockCategory);

            // Verify the result matches the mockCategory
            expect(result).toEqual(mockCategory);
        });

        test('should throw error if creation fails', async () => {
            // Arrange
            const errorMessage = 'Database error';
            Counter.getNextSequence.mockResolvedValue(1);
            Category.prototype.save.mockRejectedValue(new Error(errorMessage));

            // Act & Assert
            await expect(
                CategoryService.createCategory(mockCategoryData)
            ).rejects.toThrow(`Error creating Category: ${errorMessage}`);
        });
    });

    describe('getCategoryById', () => {
        test('should return a category by ID', async () => {
            // Arrange
            const categoryId = 'CID_1';
            const category = { categoryId, categoryName: 'Test Category' };
            Category.findOne.mockResolvedValue(category);

            // Act
            const result = await CategoryService.getCategoryById(categoryId);

            // Assert
            expect(Category.findOne).toHaveBeenCalledWith({ categoryId });
            expect(result).toEqual(category);
        });

        test('should return null if category not found', async () => {
            // Arrange
            const categoryId = 'CID_1';
            Category.findOne.mockResolvedValue(null);

            // Act
            const result = await CategoryService.getCategoryById(categoryId);

            // Assert
            expect(Category.findOne).toHaveBeenCalledWith({ categoryId });
            expect(result).toBeNull();
        });
    });

    describe('updateCategory', () => {
        test('should update an existing category', async () => {
            // Arrange
            const categoryId = 'CID_1';
            const updateData = { categoryName: 'Updated Category' };
            const updatedCategory = {
                categoryId,
                categoryName: 'Updated Category',
            };

            // Mock the methods
            Category.findOne.mockResolvedValue({ categoryId }); // Ensure category exists
            Category.findOneAndUpdate.mockResolvedValue(updatedCategory); // Mock the update method

            // Act
            const result = await CategoryService.updateCategory(
                categoryId,
                updateData
            );

            // Assert
            expect(Category.findOne).toHaveBeenCalledWith({ categoryId });
            expect(Category.findOneAndUpdate).toHaveBeenCalledWith(
                { categoryId },
                { $set: updateData },
                { new: true, runValidators: true }
            );
            expect(result).toEqual(updatedCategory);
        });

        test('should throw error if category not found', async () => {
            // Arrange
            const categoryId = 'CID_1';
            const updateData = { categoryName: 'Updated Category' };

            Category.findOne.mockResolvedValue(null); // Category not found

            // Act & Assert
            await expect(
                CategoryService.updateCategory(categoryId, updateData)
            ).rejects.toThrow('Category not found');
        });
    });

    describe('deleteCategory', () => {
        test('should delete an existing category', async () => {
            // Arrange
            const categoryId = 'CID_1';
            const deletedCategory = {
                categoryId,
                categoryName: 'Test Category',
            };

            // Mock methods for category existence and deletion
            Category.findOne.mockResolvedValue({ categoryId });
            Category.findOneAndDelete.mockResolvedValue(deletedCategory);

            // Act
            const result = await CategoryService.deleteCategory(categoryId);

            // Assert
            expect(Category.findOne).toHaveBeenCalledWith({ categoryId });
            expect(Category.findOneAndDelete).toHaveBeenCalledWith({
                categoryId,
            });
            expect(result).toEqual(deletedCategory);
        });

        test('should throw error if category not found', async () => {
            // Arrange
            const categoryId = 'CID_1';
            Category.findOne.mockResolvedValue(null); // Category not found

            // Act & Assert
            await expect(
                CategoryService.deleteCategory(categoryId)
            ).rejects.toThrow('Category not found');
        });
    });
});
