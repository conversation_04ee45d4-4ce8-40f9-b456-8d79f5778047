/* eslint-disable no-undef */
const {
    createSubCategory,
    getSubCategoryById,
    updateSubCategory,
    deleteSubCategory,
} = require('../../../src/api/v1/subCategory/subCategoryController');

const SubCategoryService = require('../../../src/api/v1/subCategory/subCategoryService');

jest.mock('../../../src/api/v1/subCategory/subCategoryService');

describe('SubCategory Controller', () => {
    let req, res;

    beforeEach(() => {
        req = { body: {}, params: {}, query: {} };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis(),
            warn: jest.fn().mockReturnThis(),
        };

        jest.clearAllMocks();
    });

    const mockServiceMethods = () => {
        SubCategoryService.createSubCategory.mockResolvedValue({
            subCategoryId: 1,
            subCategoryName: 'Test SubCategory',
            subCategorySlug: 'test-subcategory',
        });
        SubCategoryService.getSubCategoryById.mockResolvedValue({
            subCategoryId: 1,
            subCategoryName: 'Test SubCategory',
        });
        SubCategoryService.updateSubCategory.mockResolvedValue({
            subCategoryId: 1,
            subCategoryName: 'Updated SubCategory',
        });
        SubCategoryService.deleteSubCategory.mockResolvedValue();
    };

    test('createSubCategory should create a new subcategory', async () => {
        req.body = {
            subCategoryName: 'Test SubCategory',
            subCategorySlug: 'test-subcategory',
        };
        mockServiceMethods();

        await createSubCategory(req, res);

        expect(SubCategoryService.createSubCategory).toHaveBeenCalledWith(
            req.body
        );
        expect(res.status).toHaveBeenCalledWith(201);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            subCategory: {
                subCategoryId: 1,
                subCategoryName: 'Test SubCategory',
                subCategorySlug: 'test-subcategory',
            },
            message: 'SubCategory created successfully',
        });
    });

    test('getSubCategoryById should fetch a subcategory by ID', async () => {
        req.params = { subCategoryId: '1' };
        mockServiceMethods();

        await getSubCategoryById(req, res);

        expect(SubCategoryService.getSubCategoryById).toHaveBeenCalledWith('1');
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            subCategory: {
                subCategoryId: 1,
                subCategoryName: 'Test SubCategory',
            },
            message: 'SubCategory fetched successfully',
        });
    });

    test('getSubCategoryById should return 404 if subcategory not found', async () => {
        req.params = { subCategoryId: '1' };
        SubCategoryService.getSubCategoryById.mockResolvedValue(null);

        await getSubCategoryById(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            success: false,
            code: 404,
            errors: [
                { field: 'subCategoryId', message: 'SubCategory not found' },
            ],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
        });
    });

    test('updateSubCategory should update an existing subcategory', async () => {
        req.params = { subCategoryId: '1' };
        req.body = { subCategoryName: 'Updated SubCategory' };
        mockServiceMethods();

        await updateSubCategory(req, res);

        expect(SubCategoryService.updateSubCategory).toHaveBeenCalledWith(
            '1',
            req.body
        );
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            subCategory: {
                subCategoryId: 1,
                subCategoryName: 'Updated SubCategory',
            },
            message: 'SubCategory updated successfully',
        });
    });

    test('updateSubCategory should return 404 if subcategory not found', async () => {
        req.params = { subCategoryId: '1' };
        SubCategoryService.getSubCategoryById.mockResolvedValue(null);

        await updateSubCategory(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            success: false,
            code: 404,
            errors: [
                {
                    field: 'subCategoryId',
                    message: 'SubCategory not found for update',
                },
            ],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
        });
    });

    test('deleteSubCategory should delete an existing subcategory', async () => {
        req.params = { subCategoryId: '1' };
        mockServiceMethods();

        await deleteSubCategory(req, res);

        expect(SubCategoryService.deleteSubCategory).toHaveBeenCalledWith('1');
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            message: 'SubCategory deleted successfully',
        });
    });

    test('deleteSubCategory should return 404 if subcategory not found', async () => {
        req.params = { subCategoryId: '1' };
        SubCategoryService.getSubCategoryById.mockResolvedValue(null);

        await deleteSubCategory(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            success: false,
            code: 404,
            errors: [
                {
                    field: 'subCategoryId',
                    message: 'SubCategory not found for deletion',
                },
            ],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
        });
    });
});
