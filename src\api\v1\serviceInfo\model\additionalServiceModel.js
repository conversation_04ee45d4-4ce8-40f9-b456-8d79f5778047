// additionalService.js
const mongoose = require('mongoose');

const additionalServiceSchema = new mongoose.Schema({
    additionalServiceId: {
        type: String,
        required: true,
    },

    serviceId: {
        type: String,
        required: true,
    },

    serviceItem: {
        type: String,
        required: true,
    },
    price: {
        type: Number,
        required: true,
        min: 0,
    },
    images: {
        type: String,
        required: true,
    },
});

module.exports = mongoose.model('AdditionalService', additionalServiceSchema);
