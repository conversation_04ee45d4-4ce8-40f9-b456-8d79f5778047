/* eslint-disable no-undef */
const {
    createCategory,
    getCategories,
    getCategoryById,
    updateCategory,
    deleteCategory,
} = require('../../../src/api/v1/category/categoryService');
const Category = require('../../../src/api/v1/category/categoryModel');
const Counter = require('../../../src/api/v1/category/categoryCounter');
const { setupDB, teardownDB } = require('../../environment/setupTest');

beforeAll(async () => {
    await setupDB();
});

afterAll(async () => {
    await teardownDB();
});

describe('Category DAO Integration Tests', () => {
    let categoryId;

    // Test: Create a new category
    it('should create a new category', async () => {
        const categoryData = {
            categoryName: 'Test Category',
            categorySlug: 'test-category',
        };

        const savedCategory = await createCategory(categoryData);

        expect(savedCategory).toHaveProperty('categoryId');
        expect(savedCategory.categoryName).toBe(categoryData.categoryName);
        expect(savedCategory.categorySlug).toBe(categoryData.categorySlug);
        categoryId = savedCategory.categoryId; // Save category ID for further tests
    });

    // Test: Fetch category by ID using DAO
    it('should fetch category by ID', async () => {
        const category = await getCategoryById(categoryId);

        expect(category).not.toBeNull();
        expect(category).toHaveProperty('categoryId', categoryId);
    });

    // Test: Fetch all categories using DAO
    it('should fetch all categories', async () => {
        const { categories } = await getCategories(
            {},
            'categoryName',
            1,
            1,
            10
        );

        expect(categories).toBeInstanceOf(Array);
        expect(categories.length).toBeGreaterThan(0); // Ensure that at least one category exists
    });

    // Test: Update category using DAO
    it('should update category', async () => {
        const updatedCategoryData = {
            categoryName: 'Updated Test Category',
        };

        const updatedCategory = await updateCategory(
            categoryId,
            updatedCategoryData
        );

        expect(updatedCategory).not.toBeNull();
        expect(updatedCategory.categoryName).toBe(
            updatedCategoryData.categoryName
        );
    });

    // Test: Delete category using DAO
    it('should delete category', async () => {
        const deletedCategory = await deleteCategory(categoryId);

        expect(deletedCategory).not.toBeNull();
        expect(deletedCategory).toHaveProperty('categoryId', categoryId);

        // Ensure it's really deleted
        const category = await Category.findOne({ categoryId });
        expect(category).toBeNull();
    });

    // Test: Ensure counter increments for category IDs using DAO method
    it('should increment the counter for categoryId', async () => {
        const initialCounter = await Counter.getNextSequence();

        const categoryData = {
            categoryName: 'New Category with Counter',
            categorySlug: 'new-category-counter',
        };

        // Create category using DAO
        const newCategory = await createCategory(categoryData);

        const updatedCounter = await Counter.getNextSequence();

        expect(newCategory.categoryId).not.toBeNull();
        expect(updatedCounter).toBeGreaterThan(initialCounter); // Ensure the counter increments
    });
});
