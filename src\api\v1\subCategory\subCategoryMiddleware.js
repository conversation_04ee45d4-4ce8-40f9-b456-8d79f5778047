const { body } = require('express-validator');

const validateSubCategory = [
    body('subCategoryName')
        .notEmpty()
        .withMessage('subCategoryName is required.')
        .isString()
        .withMessage('subCategoryName must be a string.')
        .trim(),

    body('subCategorySlug')
        .optional()
        .isString()
        .withMessage('subCategorySlug must be a string.')
        .trim(),

    body('subCategoryImage')
        .optional()
        .isString()
        .withMessage('subCategoryImage must be a string.')
        .trim(),

    body('categoryId')
        .optional()
        .isString()
        .withMessage('categoryId must be a string.')
        .trim(),

    body('isFeatured')
        .optional()
        .isBoolean()
        .withMessage('isFeatured must be a boolean.'),
];

module.exports = {
    validateSubCategory,
};
