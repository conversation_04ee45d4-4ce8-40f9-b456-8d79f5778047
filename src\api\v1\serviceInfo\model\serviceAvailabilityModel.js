/* eslint-disable prefer-const */
/* eslint-disable id-length */
// /* eslint-disable prefer-const */
// /* eslint-disable id-length */
// /* eslint-disable id-length */
// const mongoose = require('mongoose');

// const timeSlotSchema = new mongoose.Schema({
//     from: {
//         type: String,
//         match: /^(0[0-9]|1[0-2]|[0-9]):([0-5][0-9])(\s?[APap][Mm])?$/,
//         required: true,
//     },
//     to: {
//         type: String,
//         match: /^(0[0-9]|1[0-2]|[0-9]):([0-5][0-9])(\s?[APap][Mm])?$/,
//         required: true,
//     },

//     maxBookings: {
//         type: Number,
//         default: 1,
//     },

//     booked: {
//         type: Number,
//         default: 0,
//         min: 0,
//     },
// });

// const serviceAvailabilitySchema = new mongoose.Schema({
//     serviceAvailableId: {
//         type: String,
//         required: true,
//     },
//     serviceId: {
//         type: String,
//         required: true,
//     },
//     day: {
//         type: String,
//         enum: [
//             'monday',
//             'tuesday',
//             'wednesday',
//             'thursday',
//             'friday',
//             'saturday',
//             'sunday',
//         ],
//         required: true,
//     },
//     available: {
//         type: Boolean,
//         default: false,
//     },
//     timeSlots: [timeSlotSchema],
// });

// module.exports = mongoose.model(
//     'ServiceAvailability',
//     serviceAvailabilitySchema
// );

const mongoose = require('mongoose');

const timeSlotSchema = new mongoose.Schema({
    from: {
        type: String,
        match: /^(0[0-9]|1[0-2]|[0-9]):([0-5][0-9])(\s?[APap][Mm])?$/,
        required: true,
    },
    to: {
        type: String,
        match: /^(0[0-9]|1[0-2]|[0-9]):([0-5][0-9])(\s?[APap][Mm])?$/,
        required: true,
    },

    maxBookings: {
        type: Number,
        default: 1,
    },

    booked: {
        type: Number,
        default: 0,
        min: 0,
    },

    bookingId: {
        type: String,
        default: null,
    },
});

const serviceAvailabilitySchema = new mongoose.Schema({
    serviceAvailableId: {
        type: String,
        required: true,
    },
    serviceId: {
        type: String,
        required: true,
    },
    day: {
        type: String,
        enum: [
            'monday',
            'tuesday',
            'wednesday',
            'thursday',
            'friday',
            'saturday',
            'sunday',
        ],
        required: true,
    },
    available: {
        type: Boolean,
        default: false,
    },
    timeSlots: [timeSlotSchema],
});

// Time splitting function
function splitTimeSlot(from, to, maxBookings) {
    const toMinutes = (t) => {
        const [time, modifier] = t.split(/(AM|PM)/i);
        let [hours, minutes] = time.trim().split(':').map(Number);

        if (modifier?.toUpperCase() === 'PM' && hours !== 12) hours += 12;
        if (modifier?.toUpperCase() === 'AM' && hours === 12) hours = 0;

        return hours * 60 + minutes;
    };

    const toTimeStr = (m) => {
        const hours = Math.floor(m / 60);
        const minutes = m % 60;
        const ampm = hours >= 12 ? 'PM' : 'AM';
        const h = hours % 12 || 12;
        return `${h}:${minutes.toString().padStart(2, '0')} ${ampm}`;
    };

    const start = toMinutes(from);
    const end = toMinutes(to);
    const slotDuration = Math.floor((end - start) / maxBookings);

    const slots = [];
    for (let i = 0; i < maxBookings; i++) {
        const slotStart = start + i * slotDuration;
        const slotEnd = slotStart + slotDuration;
        slots.push({
            from: toTimeStr(slotStart),
            to: toTimeStr(slotEnd),
            maxBookings: 1,
            booked: 0,
        });
    }

    return slots;
}

// Pre-save hook to split slots
serviceAvailabilitySchema.pre('save', function (next) {
    const splitSlots = [];
    this.timeSlots.forEach((slot) => {
        const result = splitTimeSlot(slot.from, slot.to, slot.maxBookings);
        splitSlots.push(...result);
    });
    this.timeSlots = splitSlots;
    next();
});

module.exports = mongoose.model(
    'ServiceAvailability',
    serviceAvailabilitySchema
);
