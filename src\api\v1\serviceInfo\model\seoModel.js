const mongoose = require('mongoose');

const seoSchema = new mongoose.Schema({
    seoId: {
        type: String,
        required: true,
    },

    serviceId: {
        type: String,
        required: true,
    },

    metaTitle: {
        type: String,
    },
    metaKeywords: [
        {
            type: String,
        },
    ],
    metaDescription: {
        type: String,
    },
});

module.exports = mongoose.model('SEO', seoSchema);
