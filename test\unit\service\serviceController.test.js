/* eslint-disable no-undef */
const {
    createServiceInformation,
    getServiceInformationById,
    updateServiceInformation,
    deleteServiceInformation,
} = require('../../../src/api/v1/serviceInfo/serviceInfoController');

const ServiceInformation = require('../../../src/api/v1/serviceInfo/service/serviceInfoService');

jest.mock('../../../src/api/v1/serviceInfo/service/serviceInfoService.js');

describe('Service Information Controller', () => {
    let req, res;

    beforeEach(() => {
        req = { body: {}, params: {}, query: {} };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis(),
        };

        jest.clearAllMocks();
    });

    const mockServiceMethods = () => {
        ServiceInformation.createServiceInformation.mockResolvedValue({
            serviceId: 1,
            serviceTitle: 'Test Service',
            categoryId: 'cat123',
            subCategoryId: 'subcat123',
            price: 50,
            duration: '1 hour',
            description: 'This is a test service description.',
            additionalServicesId: ['serviceItem1', 'serviceItem2'],
            serviceAvailableId: ['availableTimeSlot1', 'availableTimeSlot2'],
            locationId: ['location1', 'location2'],
            galleryId: ['galleryItem1', 'galleryItem2'],
            seoId: ['seoItem1', 'seoItem2'],
        });
        ServiceInformation.getServiceInformationById.mockResolvedValue({
            serviceId: 1,
            serviceTitle: 'Test Service',
        });
        ServiceInformation.updateServiceInformation.mockResolvedValue({
            serviceId: 1,
            serviceTitle: 'Updated Service',
        });
        ServiceInformation.deleteServiceInformation.mockResolvedValue();
    };

    test('createServiceInformation should create a new service information', async () => {
        // Setting up the request body according to the updated schema
        req.body = {
            serviceTitle: 'Test Service',
            categoryId: 'cat123', // example category ID
            subCategoryId: 'subcat123', // example sub-category ID
            price: 50,
            duration: '1 hour',
            description: 'This is a test service description.',
            additionalServicesId: ['serviceItem1', 'serviceItem2'], // example additional services IDs
            serviceAvailableId: ['availableTimeSlot1', 'availableTimeSlot2'], // example availability IDs
            locationId: ['location1', 'location2'], // example location IDs
            galleryId: ['galleryItem1', 'galleryItem2'], // example gallery IDs
            seoId: ['seoItem1', 'seoItem2'], // example SEO IDs
        };

        // Mocking the service method calls
        mockServiceMethods();

        // Calling the controller method
        await createServiceInformation(req, res);

        // Verifying that the service method was called with the correct parameters
        expect(
            ServiceInformation.createServiceInformation
        ).toHaveBeenCalledWith(req.body);

        // Verifying that the response status and response body are correct
        expect(res.status).toHaveBeenCalledWith(201);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            message: 'Service information created successfully',
            serviceInfo: {
                serviceId: 1,
                serviceTitle: 'Test Service',
                categoryId: 'cat123',
                subCategoryId: 'subcat123',
                price: 50,
                duration: '1 hour',
                description: 'This is a test service description.',
                additionalServicesId: ['serviceItem1', 'serviceItem2'],
                serviceAvailableId: [
                    'availableTimeSlot1',
                    'availableTimeSlot2',
                ],
                locationId: ['location1', 'location2'],
                galleryId: ['galleryItem1', 'galleryItem2'],
                seoId: ['seoItem1', 'seoItem2'],
            },
        });
    });

    test('getServiceInformationById should fetch service information by ID', async () => {
        req.params = { serviceId: '1' };
        mockServiceMethods();

        await getServiceInformationById(req, res);

        expect(
            ServiceInformation.getServiceInformationById
        ).toHaveBeenCalledWith('1');
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            message: 'Service information fetched successfully',
            serviceInfo: { serviceId: 1, serviceTitle: 'Test Service' },
        });
    });

    test('getServiceInformationById should return 404 if service information not found', async () => {
        req.params = { serviceId: '1' };
        ServiceInformation.getServiceInformationById.mockResolvedValue(null);

        await getServiceInformationById(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            code: 404,
            errors: [
                {
                    field: 'serviceId',
                    message: 'Service information not found',
                },
            ],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
            success: false,
        });
    });

    test('updateServiceInformation should update an existing service information', async () => {
        req.params = { serviceId: '1' };
        req.body = { serviceTitle: 'Updated Service' };
        mockServiceMethods();

        await updateServiceInformation(req, res);

        expect(
            ServiceInformation.updateServiceInformation
        ).toHaveBeenCalledWith('1', req.body);
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            message: 'Service information updated successfully',
            serviceInfo: { serviceId: 1, serviceTitle: 'Updated Service' },
        });
    });

    test('updateServiceInformation should return 404 if service information not found', async () => {
        req.params = { serviceId: '1' };
        ServiceInformation.getServiceInformationById.mockResolvedValue(null);

        await updateServiceInformation(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            code: 404,
            errors: [
                {
                    field: 'serviceId',
                    message: 'Service information not found',
                },
            ],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
            success: false,
        });
    });

    it('should delete an existing service information', async () => {
        // Mock the necessary methods
        ServiceInformation.getServiceInformationById.mockResolvedValue({
            serviceId: '1',
            serviceTitle: 'Test Service',
        });
        ServiceInformation.deleteServiceInformation.mockResolvedValue(true); // Simulate successful deletion

        const req = { params: { serviceId: '1' } };
        const res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn(),
        };

        // Call the controller method
        await deleteServiceInformation(req, res);

        // Check that the status and response are as expected
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            message: 'Service information deleted successfully',
        });
    });

    test('deleteServiceInformation should return 404 if service information not found', async () => {
        req.params = { serviceId: '1' };
        ServiceInformation.getServiceInformationById.mockResolvedValue(null);

        await deleteServiceInformation(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            code: 404,
            errors: [
                {
                    field: 'serviceId',
                    message: 'Service information not found',
                },
            ],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
            success: false,
        });
    });
});
