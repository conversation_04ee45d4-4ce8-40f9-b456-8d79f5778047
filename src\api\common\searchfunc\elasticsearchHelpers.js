/* eslint-disable id-length */
const logger = require('../utils/logger');

const client = require('./elasticsearchClient');

// Function to index a document
async function indexDocument(index, id, document) {
    try {
        const { ...docWithoutId } = document;

        const result = await client.index({
            index,
            id,
            body: docWithoutId,
        });
        logger.info('Document indexed:', result);
    } catch (error) {
        logger.error('Error indexing document:', error);
    }
}

// Function to search documents
async function searchDocuments(index, query) {
    try {
        const result = await client.search({
            index,
            body: query,
        });
        return result;
    } catch (error) {
        logger.error('Error searching documents:', error);
    }
    return [];
}

const updateDocument = async (index, id, updatedFields) => {
    logger.info(
        `Updating document with ID: ${id} in index: ${index} using updated fields: ${JSON.stringify(updatedFields)}`
    );
    try {
        const { ...docWithoutId } = updatedFields;
        const response = await client.update({
            index,
            id,
            body: {
                doc: docWithoutId, // Ensure the fields are wrapped inside `doc`
            },
        });
        logger.info(`Document with ID: ${response} updated successfully`);
        return response; // Ensure this returns the Elasticsearch response
    } catch (error) {
        logger.error(
            `Error updating document with ID: ${id} - ${error.message}`
        );
        throw error;
    }
};

const deleteDocument = async (index, id) => {
    logger.info(`Deleting document with ID: ${id} from index: ${index}`);
    try {
        const response = await client.delete({
            index: index,
            id: id,
        });
        return response;
    } catch (error) {
        logger.error(
            `Error deleting document with ID: ${id} - ${error.message}`
        );
        throw error;
    }
};

module.exports = {
    indexDocument,
    searchDocuments,
    updateDocument,
    deleteDocument,
};
