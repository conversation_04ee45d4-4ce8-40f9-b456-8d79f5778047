const ReviewService = require('../../../src/api/v1/Reviews/service/reviewService');
const Review = require('../../../src/api/v1/Reviews/model/reviewModel');
const { getNextSequenceValue } = require('../../../src/api/v1/Reviews/counter/reviewCounter');

// Mock dependencies
jest.mock('../../../src/api/v1/Reviews/model/reviewModel');
jest.mock('../../../src/api/v1/Reviews/counter/reviewCounter');
jest.mock('../../../src/api/common/utils/logger');

describe('ReviewService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('createReview', () => {
        it('should create a review successfully', async () => {
            const mockReviewData = {
                serviceId: 'SRV_001',
                bookingId: 'BKG_001',
                providerId: 'PRV_001',
                rating: 5,
                title: 'Excellent service',
                comment: 'The service was outstanding and exceeded my expectations.',
                images: [
                    {
                        imageUrl: 'https://example.com/image1.jpg',
                        imageCaption: 'Before photo'
                    }
                ]
            };

            const customerId = 'CUST_001';
            const mockSequenceValue = 1;
            const expectedReviewId = 'REV_000001';

            // Mock dependencies
            Review.findOne.mockResolvedValue(null); // No existing review
            getNextSequenceValue.mockResolvedValue(mockSequenceValue);
            
            const mockSavedReview = {
                reviewId: expectedReviewId,
                customerId,
                ...mockReviewData,
                save: jest.fn().mockResolvedValue({
                    reviewId: expectedReviewId,
                    customerId,
                    ...mockReviewData
                })
            };

            Review.mockImplementation(() => mockSavedReview);

            // Execute
            const result = await ReviewService.createReview(mockReviewData, customerId);

            // Verify
            expect(Review.findOne).toHaveBeenCalledWith({
                customerId,
                bookingId: mockReviewData.bookingId,
                isDeleted: false,
            });
            expect(getNextSequenceValue).toHaveBeenCalledWith('reviewId');
            expect(mockSavedReview.save).toHaveBeenCalled();
            expect(result.reviewId).toBe(expectedReviewId);
        });

        it('should throw error if customer already reviewed the booking', async () => {
            const mockReviewData = {
                serviceId: 'SRV_001',
                bookingId: 'BKG_001',
                providerId: 'PRV_001',
                rating: 5,
                comment: 'Great service'
            };

            const customerId = 'CUST_001';

            // Mock existing review
            Review.findOne.mockResolvedValue({ reviewId: 'REV_000001' });

            // Execute and verify
            await expect(ReviewService.createReview(mockReviewData, customerId))
                .rejects.toThrow('You have already reviewed this booking.');
        });
    });

    describe('getReviews', () => {
        it('should fetch reviews with pagination', async () => {
            const mockReviews = [
                { reviewId: 'REV_000001', rating: 5, comment: 'Great!' },
                { reviewId: 'REV_000002', rating: 4, comment: 'Good service' }
            ];

            const mockQuery = { serviceId: 'SRV_001' };
            const page = 1;
            const limit = 10;

            // Mock Review methods
            const mockFind = {
                sort: jest.fn().mockReturnThis(),
                skip: jest.fn().mockReturnThis(),
                limit: jest.fn().mockReturnThis(),
                lean: jest.fn().mockResolvedValue(mockReviews)
            };

            Review.find.mockReturnValue(mockFind);
            Review.countDocuments.mockResolvedValue(2);

            // Execute
            const result = await ReviewService.getReviews(mockQuery, 'reviewDate', -1, page, limit);

            // Verify
            expect(Review.find).toHaveBeenCalledWith({ isDeleted: false, ...mockQuery });
            expect(mockFind.sort).toHaveBeenCalledWith({ reviewDate: -1 });
            expect(mockFind.skip).toHaveBeenCalledWith(0);
            expect(mockFind.limit).toHaveBeenCalledWith(10);
            expect(result.reviews).toEqual(mockReviews);
            expect(result.total).toBe(2);
            expect(result.page).toBe(1);
            expect(result.pages).toBe(1);
        });
    });

    describe('getReviewById', () => {
        it('should fetch review by ID successfully', async () => {
            const reviewId = 'REV_000001';
            const mockReview = {
                reviewId,
                rating: 5,
                comment: 'Excellent service'
            };

            Review.findOne.mockReturnValue({
                lean: jest.fn().mockResolvedValue(mockReview)
            });

            // Execute
            const result = await ReviewService.getReviewById(reviewId);

            // Verify
            expect(Review.findOne).toHaveBeenCalledWith({
                reviewId,
                isDeleted: false
            });
            expect(result).toEqual(mockReview);
        });

        it('should throw error if review not found', async () => {
            const reviewId = 'REV_999999';

            Review.findOne.mockReturnValue({
                lean: jest.fn().mockResolvedValue(null)
            });

            // Execute and verify
            await expect(ReviewService.getReviewById(reviewId))
                .rejects.toThrow('Review not found.');
        });
    });

    describe('updateReview', () => {
        it('should update review successfully', async () => {
            const reviewId = 'REV_000001';
            const customerId = 'CUST_001';
            const updateData = {
                rating: 4,
                comment: 'Updated comment'
            };

            const mockReview = {
                reviewId,
                customerId,
                rating: 5,
                comment: 'Original comment',
                isEdited: false,
                save: jest.fn().mockResolvedValue({
                    reviewId,
                    customerId,
                    ...updateData,
                    isEdited: true
                })
            };

            Review.findOne.mockResolvedValue(mockReview);

            // Execute
            const result = await ReviewService.updateReview(reviewId, updateData, customerId);

            // Verify
            expect(Review.findOne).toHaveBeenCalledWith({
                reviewId,
                customerId,
                isDeleted: false
            });
            expect(mockReview.rating).toBe(updateData.rating);
            expect(mockReview.comment).toBe(updateData.comment);
            expect(mockReview.isEdited).toBe(true);
            expect(mockReview.save).toHaveBeenCalled();
        });

        it('should throw error if review not found or no permission', async () => {
            const reviewId = 'REV_000001';
            const customerId = 'CUST_002'; // Different customer
            const updateData = { rating: 4 };

            Review.findOne.mockResolvedValue(null);

            // Execute and verify
            await expect(ReviewService.updateReview(reviewId, updateData, customerId))
                .rejects.toThrow('Review not found or you do not have permission to update it.');
        });
    });

    describe('getReviewAnalytics', () => {
        it('should return analytics for service reviews', async () => {
            const filters = { serviceId: 'SRV_001' };
            const mockAnalytics = [{
                totalReviews: 10,
                averageRating: 4.5,
                totalHelpfulVotes: 25,
                ratingBreakdown: {
                    '5': 5,
                    '4': 3,
                    '3': 1,
                    '2': 1,
                    '1': 0
                }
            }];

            Review.aggregate.mockResolvedValue(mockAnalytics);

            // Execute
            const result = await ReviewService.getReviewAnalytics(filters);

            // Verify
            expect(Review.aggregate).toHaveBeenCalled();
            expect(result).toEqual(mockAnalytics[0]);
        });

        it('should return default analytics when no reviews found', async () => {
            const filters = { serviceId: 'SRV_999' };

            Review.aggregate.mockResolvedValue([]);

            // Execute
            const result = await ReviewService.getReviewAnalytics(filters);

            // Verify
            expect(result).toEqual({
                totalReviews: 0,
                averageRating: 0,
                totalHelpfulVotes: 0,
                ratingBreakdown: { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0 }
            });
        });
    });

    describe('getOverallRating', () => {
        it('should return overall rating with percentages', async () => {
            const filters = { serviceId: 'SRV_001' };
            const mockAnalytics = [{
                totalReviews: 20,
                averageRating: 4.3,
                ratingBreakdown: {
                    '5': { count: 8, percentage: 40.0 },
                    '4': { count: 6, percentage: 30.0 },
                    '3': { count: 4, percentage: 20.0 },
                    '2': { count: 2, percentage: 10.0 },
                    '1': { count: 0, percentage: 0.0 }
                }
            }];

            Review.aggregate.mockResolvedValue(mockAnalytics);

            const result = await ReviewService.getOverallRating(filters);

            expect(Review.aggregate).toHaveBeenCalled();
            expect(result.totalReviews).toBe(20);
            expect(result.averageRating).toBe(4.3);
            expect(result.ratingQuality).toBe('Very Good');
            expect(result.recommendationPercentage).toBe(70);
            expect(result.ratingBreakdown['5'].count).toBe(8);
            expect(result.ratingBreakdown['5'].percentage).toBe(40.0);
        });

        it('should return default values when no reviews exist', async () => {
            Review.aggregate.mockResolvedValue([]);

            const result = await ReviewService.getOverallRating({ serviceId: 'SRV_002' });

            expect(result).toEqual({
                totalReviews: 0,
                averageRating: 0,
                ratingBreakdown: {
                    '5': { count: 0, percentage: 0 },
                    '4': { count: 0, percentage: 0 },
                    '3': { count: 0, percentage: 0 },
                    '2': { count: 0, percentage: 0 },
                    '1': { count: 0, percentage: 0 }
                },
                ratingQuality: 'Poor',
                recommendationPercentage: 0
            });
        });
    });

    describe('getMultipleRatingSummaries', () => {
        it('should return summaries for multiple services', async () => {
            const itemIds = ['SRV_001', 'SRV_002', 'SRV_003'];

            Review.aggregate.mockResolvedValue([
                { itemId: 'SRV_001', totalReviews: 15, averageRating: 4.2 },
                { itemId: 'SRV_002', totalReviews: 8, averageRating: 3.8 }
            ]);

            const result = await ReviewService.getMultipleRatingSummaries(itemIds, 'serviceId');

            expect(Review.aggregate).toHaveBeenCalled();
            expect(result).toHaveLength(3);
            expect(result[0]).toEqual({
                itemId: 'SRV_001',
                totalReviews: 15,
                averageRating: 4.2,
                ratingQuality: 'Very Good'
            });
            expect(result[1]).toEqual({
                itemId: 'SRV_002',
                totalReviews: 8,
                averageRating: 3.8,
                ratingQuality: 'Good'
            });
            expect(result[2]).toEqual({
                itemId: 'SRV_003',
                totalReviews: 0,
                averageRating: 0,
                ratingQuality: 'No Reviews'
            });
        });

        it('should handle provider IDs', async () => {
            const itemIds = ['PRV_001'];
            Review.aggregate.mockResolvedValue([
                { itemId: 'PRV_001', totalReviews: 25, averageRating: 4.5 }
            ]);

            const result = await ReviewService.getMultipleRatingSummaries(itemIds, 'providerId');

            expect(result[0]).toEqual({
                itemId: 'PRV_001',
                totalReviews: 25,
                averageRating: 4.5,
                ratingQuality: 'Excellent'
            });
        });
    });
});
