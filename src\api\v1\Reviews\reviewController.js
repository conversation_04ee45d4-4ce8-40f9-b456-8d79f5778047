const { validationResult } = require('express-validator');
const ReviewService = require('./service/reviewService');
const {
    buildQueryFilter,
    validateAndParseParams,
} = require('../../common/utils/filter');
const logger = require('../../common/utils/logger');
const errorUtil = require('../../common/utils/error');

/**
 * Create a new review
 */
const createReview = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for review creation: ${JSON.stringify(formattedErrors)}`,
                { errorId, formattedErrors }
            );

            return res.status(422).json(errorResponse);
        }

        const customerId = req.userData.user.userId;
        const review = await ReviewService.createReview(req.body, customerId);

        logger.info(`Review created successfully. ID: ${review.reviewId}`);
        return res.status(201).json({
            success: true,
            message: 'Review created successfully',
            review,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR,
            400,
            errorId
        );

        logger.error(`Error creating review: ${error.message}`, { errorId });
        return res.status(400).json(errorResponse);
    }
};



/**
 * Get reviews with filtering and pagination
 */
const getReviews = async (req, res) => {
    try {
        const {
            page = '1',
            limit = '10',
            search,
            rating,
            status = 'approved',
            serviceId,
            providerId,
            customerId,
            sortBy = 'reviewDate',
            sortOrder = 'desc',
        } = req.query;

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );

        // Build query filter
        const query = {};
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { comment: { $regex: search, $options: 'i' } },
            ];
        }
        if (rating) query.rating = parseInt(rating);
        if (status) query.status = status;
        if (serviceId) query.serviceId = serviceId;
        if (providerId) query.providerId = providerId;
        if (customerId) query.customerId = customerId;

        const result = await ReviewService.getReviews(
            query,
            sortBy,
            sortDirection,
            pageNum,
            limitNum
        );

        logger.info(`Fetched ${result.reviews.length} reviews from page ${pageNum}`);
        return res.status(200).json({
            success: true,
            message: 'Reviews fetched successfully',
            ...result,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        logger.error(`Error fetching reviews: ${error.message}`, { errorId });
        return res.status(500).json(errorResponse);
    }
};

/**
 * Get review by ID
 */
const getReviewById = async (req, res) => {
    try {
        const { reviewId } = req.params;
        const review = await ReviewService.getReviewById(reviewId);

        logger.info(`Fetched review with ID: ${reviewId}`);
        return res.status(200).json({
            success: true,
            message: 'Review fetched successfully',
            review,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const statusCode = error.message.includes('not found') ? 404 : 500;
        const errorType = error.message.includes('not found') 
            ? errorUtil.ERROR_TYPES.NOT_FOUND_ERROR 
            : errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR;

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'reviewId', message: error.message }],
            errorType,
            statusCode,
            errorId
        );

        logger.error(`Error fetching review by ID: ${error.message}`, { errorId });
        return res.status(statusCode).json(errorResponse);
    }
};

/**
 * Update review
 */
const updateReview = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for review update: ${JSON.stringify(formattedErrors)}`,
                { errorId, formattedErrors }
            );

            return res.status(422).json(errorResponse);
        }

        const { reviewId } = req.params;
        const customerId = req.userData.user.userId;
        const review = await ReviewService.updateReview(reviewId, req.body, customerId);

        logger.info(`Review updated successfully. ID: ${reviewId}`);
        return res.status(200).json({
            success: true,
            message: 'Review updated successfully',
            review,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const statusCode = error.message.includes('not found') || error.message.includes('permission') ? 404 : 400;
        const errorType = error.message.includes('not found') || error.message.includes('permission')
            ? errorUtil.ERROR_TYPES.NOT_FOUND_ERROR 
            : errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR;

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorType,
            statusCode,
            errorId
        );

        logger.error(`Error updating review: ${error.message}`, { errorId });
        return res.status(statusCode).json(errorResponse);
    }
};

/**
 * Delete review
 */
const deleteReview = async (req, res) => {
    try {
        const { reviewId } = req.params;
        const userId = req.userData.user.userId;
        const userType = req.userData.user.userType || 'customer';

        const review = await ReviewService.deleteReview(reviewId, userId, userType);

        logger.info(`Review deleted successfully. ID: ${reviewId}`);
        return res.status(200).json({
            success: true,
            message: 'Review deleted successfully',
            review,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const statusCode = error.message.includes('not found') || error.message.includes('permission') ? 404 : 400;
        const errorType = error.message.includes('not found') || error.message.includes('permission')
            ? errorUtil.ERROR_TYPES.NOT_FOUND_ERROR 
            : errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR;

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorType,
            statusCode,
            errorId
        );

        logger.error(`Error deleting review: ${error.message}`, { errorId });
        return res.status(statusCode).json(errorResponse);
    }
};

/**
 * Add provider response to review
 */
const addProviderResponse = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for provider response: ${JSON.stringify(formattedErrors)}`,
                { errorId, formattedErrors }
            );

            return res.status(422).json(errorResponse);
        }

        const { reviewId } = req.params;
        const providerId = req.userData.user.userId;
        const review = await ReviewService.addProviderResponse(reviewId, req.body, providerId);

        logger.info(`Provider response added successfully. Review ID: ${reviewId}`);
        return res.status(200).json({
            success: true,
            message: 'Provider response added successfully',
            review,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const statusCode = error.message.includes('not found') || error.message.includes('permission') ? 404 : 400;
        const errorType = error.message.includes('not found') || error.message.includes('permission')
            ? errorUtil.ERROR_TYPES.NOT_FOUND_ERROR
            : errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR;

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorType,
            statusCode,
            errorId
        );

        logger.error(`Error adding provider response: ${error.message}`, { errorId });
        return res.status(statusCode).json(errorResponse);
    }
};

/**
 * Add helpful vote to review
 */
const addHelpfulVote = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for helpful vote: ${JSON.stringify(formattedErrors)}`,
                { errorId, formattedErrors }
            );

            return res.status(422).json(errorResponse);
        }

        const { reviewId } = req.params;
        const userId = req.userData.user.userId;
        const review = await ReviewService.addHelpfulVote(reviewId, req.body, userId);

        logger.info(`Helpful vote added successfully. Review ID: ${reviewId}`);
        return res.status(200).json({
            success: true,
            message: 'Helpful vote added successfully',
            review,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const statusCode = error.message.includes('not found') ? 404 : 400;
        const errorType = error.message.includes('not found')
            ? errorUtil.ERROR_TYPES.NOT_FOUND_ERROR
            : errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR;

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorType,
            statusCode,
            errorId
        );

        logger.error(`Error adding helpful vote: ${error.message}`, { errorId });
        return res.status(statusCode).json(errorResponse);
    }
};

/**
 * Get review analytics
 */
const getReviewAnalytics = async (req, res) => {
    try {
        const { serviceId, providerId } = req.query;

        if (!serviceId && !providerId) {
            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'query', message: 'Either serviceId or providerId is required' }],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );

            return res.status(400).json(errorResponse);
        }

        const filters = {};
        if (serviceId) filters.serviceId = serviceId;
        if (providerId) filters.providerId = providerId;

        const analytics = await ReviewService.getReviewAnalytics(filters);

        logger.info(`Fetched review analytics for filters: ${JSON.stringify(filters)}`);
        return res.status(200).json({
            success: true,
            message: 'Review analytics fetched successfully',
            analytics,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        logger.error(`Error fetching review analytics: ${error.message}`, { errorId });
        return res.status(500).json(errorResponse);
    }
};

/**
 * Get overall rating summary for a service or provider
 */
const getOverallRating = async (req, res) => {
    try {
        const { serviceId, providerId } = req.query;

        if (!serviceId && !providerId) {
            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'query', message: 'Either serviceId or providerId is required' }],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );

            return res.status(400).json(errorResponse);
        }

        const filters = {};
        if (serviceId) filters.serviceId = serviceId;
        if (providerId) filters.providerId = providerId;

        const ratingData = await ReviewService.getOverallRating(filters);

        logger.info(`Fetched overall rating for filters: ${JSON.stringify(filters)}`);
        return res.status(200).json({
            success: true,
            message: 'Overall rating fetched successfully',
            rating: ratingData,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        logger.error(`Error fetching overall rating: ${error.message}`, { errorId });
        return res.status(500).json(errorResponse);
    }
};

/**
 * Get rating summaries for multiple items
 */
const getMultipleRatings = async (req, res) => {
    try {
        const { serviceIds, providerIds } = req.query;

        if (!serviceIds && !providerIds) {
            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'query', message: 'Either serviceIds or providerIds is required' }],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );

            return res.status(400).json(errorResponse);
        }

        let itemIds, itemType;
        if (serviceIds) {
            itemIds = Array.isArray(serviceIds) ? serviceIds : serviceIds.split(',');
            itemType = 'serviceId';
        } else {
            itemIds = Array.isArray(providerIds) ? providerIds : providerIds.split(',');
            itemType = 'providerId';
        }

        // Limit the number of items to prevent abuse
        if (itemIds.length > 50) {
            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'query', message: 'Maximum 50 items allowed per request' }],
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                400,
                errorId
            );

            return res.status(400).json(errorResponse);
        }

        const ratings = await ReviewService.getMultipleRatingSummaries(itemIds, itemType);

        logger.info(`Fetched multiple ratings for ${itemIds.length} ${itemType}s`);
        return res.status(200).json({
            success: true,
            message: 'Multiple ratings fetched successfully',
            ratings,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        logger.error(`Error fetching multiple ratings: ${error.message}`, { errorId });
        return res.status(500).json(errorResponse);
    }
};

/**
 * Moderate review (admin only)
 */
const moderateReview = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId();
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );

            logger.error(
                `Validation error for review moderation: ${JSON.stringify(formattedErrors)}`,
                { errorId, formattedErrors }
            );

            return res.status(422).json(errorResponse);
        }

        const { reviewId } = req.params;
        const adminId = req.userData.user.userId;
        const review = await ReviewService.moderateReview(reviewId, req.body, adminId);

        logger.info(`Review moderated successfully. ID: ${reviewId}`);
        return res.status(200).json({
            success: true,
            message: 'Review moderated successfully',
            review,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const statusCode = error.message.includes('not found') ? 404 : 400;
        const errorType = error.message.includes('not found')
            ? errorUtil.ERROR_TYPES.NOT_FOUND_ERROR
            : errorUtil.ERROR_TYPES.BAD_REQUEST_ERROR;

        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorType,
            statusCode,
            errorId
        );

        logger.error(`Error moderating review: ${error.message}`, { errorId });
        return res.status(statusCode).json(errorResponse);
    }
};

/**
 * Get reviews with enhanced frontend data
 */
const getReviewsForFrontend = async (req, res) => {
    try {
        const options = {
            serviceId: req.query.serviceId,
            providerId: req.query.providerId,
            customerId: req.query.customerId,
            rating: req.query.rating,
            hasImages: req.query.hasImages === 'true' ? true : req.query.hasImages === 'false' ? false : null,
            hasProviderResponse: req.query.hasProviderResponse === 'true' ? true : req.query.hasProviderResponse === 'false' ? false : null,
            sortBy: req.query.sortBy || 'reviewDate',
            sortOrder: req.query.sortOrder || 'desc',
            page: parseInt(req.query.page) || 1,
            limit: parseInt(req.query.limit) || 10,
            search: req.query.search,
            dateFrom: req.query.dateFrom,
            dateTo: req.query.dateTo,
            minHelpfulVotes: req.query.minHelpfulVotes
        };

        const result = await ReviewService.getReviewsForFrontend(options);

        logger.info(`Fetched ${result.reviews.length} reviews for frontend from page ${options.page}`);
        return res.status(200).json({
            success: true,
            message: 'Reviews fetched successfully for frontend',
            data: result
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId();
        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'general', message: error.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );

        logger.error(`Error fetching reviews for frontend: ${error.message}`, { errorId });
        return res.status(500).json(errorResponse);
    }
};

module.exports = {
    createReview,
    getReviews,
    getReviewById,
    getReviewsForFrontend,
    updateReview,
    deleteReview,
    addProviderResponse,
    addHelpfulVote,
    getReviewAnalytics,
    getOverallRating,
    getMultipleRatings,
    moderateReview,
};
