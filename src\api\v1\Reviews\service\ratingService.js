const Review = require('../model/reviewModel');
const logger = require('../../../common/utils/logger');

/**
 * Get quick rating summary for a single item
 * @param {string} itemId - Service or provider ID
 * @param {string} itemType - Type of item ('serviceId' or 'providerId')
 * @returns {Promise<Object>} Rating summary
 */
const getQuickRatingSummary = async (itemId, itemType) => {
    try {
        const matchStage = {
            isDeleted: false,
            status: 'approved',
            [itemType]: itemId
        };

        const summary = await Review.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    totalReviews: { $sum: 1 },
                    averageRating: { $avg: '$rating' },
                    ratingDistribution: { $push: '$rating' }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalReviews: 1,
                    averageRating: { $round: ['$averageRating', 2] },
                    starCounts: {
                        '5': {
                            $size: {
                                $filter: {
                                    input: '$ratingDistribution',
                                    cond: { $eq: ['$$this', 5] }
                                }
                            }
                        },
                        '4': {
                            $size: {
                                $filter: {
                                    input: '$ratingDistribution',
                                    cond: { $eq: ['$$this', 4] }
                                }
                            }
                        },
                        '3': {
                            $size: {
                                $filter: {
                                    input: '$ratingDistribution',
                                    cond: { $eq: ['$$this', 3] }
                                }
                            }
                        },
                        '2': {
                            $size: {
                                $filter: {
                                    input: '$ratingDistribution',
                                    cond: { $eq: ['$$this', 2] }
                                }
                            }
                        },
                        '1': {
                            $size: {
                                $filter: {
                                    input: '$ratingDistribution',
                                    cond: { $eq: ['$$this', 1] }
                                }
                            }
                        }
                    }
                }
            }
        ]);

        const result = summary[0] || {
            totalReviews: 0,
            averageRating: 0,
            starCounts: { '5': 0, '4': 0, '3': 0, '2': 0, '1': 0 }
        };

        // Add quality indicator
        result.ratingQuality = getRatingQuality(result.averageRating);

        logger.info(`Quick rating summary fetched for ${itemType}: ${itemId}`);
        return result;
    } catch (error) {
        logger.error(`Error fetching quick rating summary: ${error.message}`);
        throw error;
    }
};

/**
 * Get rating statistics for dashboard/analytics
 * @param {Object} filters - Filters (serviceId, providerId, dateRange)
 * @returns {Promise<Object>} Rating statistics
 */
const getRatingStatistics = async (filters) => {
    try {
        const matchStage = {
            isDeleted: false,
            status: 'approved',
            ...filters
        };

        // Add date range filter if provided
        if (filters.startDate || filters.endDate) {
            matchStage.reviewDate = {};
            if (filters.startDate) matchStage.reviewDate.$gte = new Date(filters.startDate);
            if (filters.endDate) matchStage.reviewDate.$lte = new Date(filters.endDate);
        }

        const stats = await Review.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    totalReviews: { $sum: 1 },
                    averageRating: { $avg: '$rating' },
                    highestRating: { $max: '$rating' },
                    lowestRating: { $min: '$rating' },
                    ratingDistribution: { $push: '$rating' },
                    totalHelpfulVotes: { $sum: '$helpfulCount' }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalReviews: 1,
                    averageRating: { $round: ['$averageRating', 2] },
                    highestRating: 1,
                    lowestRating: 1,
                    totalHelpfulVotes: 1,
                    ratingBreakdown: {
                        $let: {
                            vars: {
                                ratings: '$ratingDistribution',
                                total: '$totalReviews'
                            },
                            in: {
                                '5': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 5] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 5] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                },
                                '4': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 4] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 4] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                },
                                '3': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 3] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 3] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                },
                                '2': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 2] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 2] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                },
                                '1': {
                                    count: {
                                        $size: {
                                            $filter: {
                                                input: '$$ratings',
                                                cond: { $eq: ['$$this', 1] }
                                            }
                                        }
                                    },
                                    percentage: {
                                        $cond: {
                                            if: { $eq: ['$$total', 0] },
                                            then: 0,
                                            else: {
                                                $round: [
                                                    {
                                                        $multiply: [
                                                            {
                                                                $divide: [
                                                                    {
                                                                        $size: {
                                                                            $filter: {
                                                                                input: '$$ratings',
                                                                                cond: { $eq: ['$$this', 1] }
                                                                            }
                                                                        }
                                                                    },
                                                                    '$$total'
                                                                ]
                                                            },
                                                            100
                                                        ]
                                                    },
                                                    1
                                                ]
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        ]);

        const result = stats[0] || {
            totalReviews: 0,
            averageRating: 0,
            highestRating: 0,
            lowestRating: 0,
            totalHelpfulVotes: 0,
            ratingBreakdown: {
                '5': { count: 0, percentage: 0 },
                '4': { count: 0, percentage: 0 },
                '3': { count: 0, percentage: 0 },
                '2': { count: 0, percentage: 0 },
                '1': { count: 0, percentage: 0 }
            }
        };

        // Add additional metrics
        result.ratingQuality = getRatingQuality(result.averageRating);
        result.recommendationPercentage = getRecommendationPercentage(result.ratingBreakdown);
        result.satisfactionScore = getSatisfactionScore(result.ratingBreakdown);

        logger.info(`Rating statistics fetched for filters: ${JSON.stringify(filters)}`);
        return result;
    } catch (error) {
        logger.error(`Error fetching rating statistics: ${error.message}`);
        throw error;
    }
};

/**
 * Get rating quality indicator based on average rating
 * @param {number} averageRating - Average rating value
 * @returns {string} Quality indicator
 */
const getRatingQuality = (averageRating) => {
    if (averageRating >= 4.5) return 'Excellent';
    if (averageRating >= 4.0) return 'Very Good';
    if (averageRating >= 3.5) return 'Good';
    if (averageRating >= 3.0) return 'Average';
    if (averageRating >= 2.0) return 'Below Average';
    return 'Poor';
};

/**
 * Calculate recommendation percentage based on 4-5 star ratings
 * @param {Object} ratingBreakdown - Rating breakdown object
 * @returns {number} Recommendation percentage
 */
const getRecommendationPercentage = (ratingBreakdown) => {
    const positiveRatings = ratingBreakdown['5'].count + ratingBreakdown['4'].count;
    const totalRatings = Object.values(ratingBreakdown).reduce((sum, rating) => sum + rating.count, 0);
    
    if (totalRatings === 0) return 0;
    return Math.round((positiveRatings / totalRatings) * 100);
};

/**
 * Calculate satisfaction score based on weighted ratings
 * @param {Object} ratingBreakdown - Rating breakdown object
 * @returns {number} Satisfaction score (0-100)
 */
const getSatisfactionScore = (ratingBreakdown) => {
    const totalRatings = Object.values(ratingBreakdown).reduce((sum, rating) => sum + rating.count, 0);
    
    if (totalRatings === 0) return 0;
    
    const weightedSum = 
        (ratingBreakdown['5'].count * 5) +
        (ratingBreakdown['4'].count * 4) +
        (ratingBreakdown['3'].count * 3) +
        (ratingBreakdown['2'].count * 2) +
        (ratingBreakdown['1'].count * 1);
    
    return Math.round((weightedSum / (totalRatings * 5)) * 100);
};

module.exports = {
    getQuickRatingSummary,
    getRatingStatistics,
    getRatingQuality,
    getRecommendationPercentage,
    getSatisfactionScore,
};
