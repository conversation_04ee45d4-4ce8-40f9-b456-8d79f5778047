/* eslint-disable no-undef */
const {
    createCategory,
    getCategoryById,
    updateCategory,
    deleteCategory,
} = require('../../../src/api/v1/category/categoryController');

const CategoryService = require('../../../src/api/v1/category/categoryService');

jest.mock('../../../src/api/v1/category/categoryService');

describe('Category Controller', () => {
    let req, res;

    beforeEach(() => {
        req = { body: {}, params: {}, query: {} };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis(),
            warn: jest.fn().mockReturnThis(),
        };

        jest.clearAllMocks();
    });

    const mockServiceMethods = () => {
        CategoryService.createCategory.mockResolvedValue({
            categoryId: 1,
            categoryName: 'Test Category',
            categorySlug: 'test-category',
        });
        CategoryService.getCategoryById.mockResolvedValue({
            categoryId: 1,
            categoryName: 'Test Category',
        });
        CategoryService.updateCategory.mockResolvedValue({
            categoryId: 1,
            categoryName: 'Updated Category',
        });
        CategoryService.deleteCategory.mockResolvedValue();
    };

    test('createCategory should create a new category', async () => {
        req.body = {
            categoryName: 'Test Category',
            categorySlug: 'test-category',
        };
        mockServiceMethods();

        await createCategory(req, res);

        expect(CategoryService.createCategory).toHaveBeenCalledWith(req.body);
        expect(res.status).toHaveBeenCalledWith(201);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            category: {
                categoryId: 1,
                categoryName: 'Test Category',
                categorySlug: 'test-category',
            },
            message: 'Category created successfully',
        });
    });

    test('getCategoryById should fetch a category by ID', async () => {
        req.params = { categoryId: '1' };
        mockServiceMethods();

        await getCategoryById(req, res);

        expect(CategoryService.getCategoryById).toHaveBeenCalledWith('1');
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            category: { categoryId: 1, categoryName: 'Test Category' },
            message: 'Category fetched successfully',
        });
    });

    test('getCategoryById should return 404 if category not found', async () => {
        req.params = { categoryId: '1' };
        CategoryService.getCategoryById.mockResolvedValue(null);

        await getCategoryById(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            success: false,
            code: 404,
            errors: [{ field: 'categoryId', message: 'Category not found' }],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
        });
    });

    test('updateCategory should update an existing category', async () => {
        req.params = { categoryId: '1' };
        req.body = { categoryName: 'Updated Category' };
        mockServiceMethods();

        await updateCategory(req, res);

        expect(CategoryService.updateCategory).toHaveBeenCalledWith(
            '1',
            req.body
        );
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            category: { categoryId: 1, categoryName: 'Updated Category' },
            message: 'Category updated successfully',
        });
    });

    test('updateCategory should return 404 if category not found', async () => {
        req.params = { categoryId: '1' };
        CategoryService.getCategoryById.mockResolvedValue(null);

        await updateCategory(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            success: false,
            code: 404,
            errors: [
                {
                    field: 'categoryId',
                    message: 'Category not found for update',
                },
            ],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
        });
    });

    test('deleteCategory should delete an existing category', async () => {
        req.params = { categoryId: '1' };
        mockServiceMethods();

        await deleteCategory(req, res);

        expect(CategoryService.deleteCategory).toHaveBeenCalledWith('1');
        expect(res.status).toHaveBeenCalledWith(200);
        expect(res.json).toHaveBeenCalledWith({
            success: true,
            message: 'Category deleted successfully',
        });
    });

    test('deleteCategory should return 404 if category not found', async () => {
        req.params = { categoryId: '1' };
        CategoryService.getCategoryById.mockResolvedValue(null);

        await deleteCategory(req, res);

        expect(res.status).toHaveBeenCalledWith(404);
        expect(res.json).toHaveBeenCalledWith({
            success: false,
            code: 404,
            errors: [
                {
                    field: 'categoryId',
                    message: 'Category not found for deletion',
                },
            ],
            message: expect.stringMatching(
                /.*The requested resource could not be found. Please check the URL and try again.*/
            ),
        });
    });
});
