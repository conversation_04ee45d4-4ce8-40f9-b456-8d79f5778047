/* eslint-disable prettier/prettier */
/* eslint-disable id-length */
/* eslint-disable no-undef */
const ServiceInfoService = require('../../../src/api/v1/serviceInfo/service/serviceInfoService.js');
const ServiceInfo = require('../../../src/api/v1/serviceInfo/model/serviceInfoModel.js');
const serviceInfoFetcher = require('../../../src/api/v1/serviceInfo/service/serviceInfoFetcher.js');
const relatedDataService = require('../../../src/api/v1/serviceInfo/service/relatedDataService.js');
const StaffService = require('../../../src/api/v1/staff/staffService.js');
const ServiceInfoCounter = require('../../../src/api/v1/serviceInfo/counter/serviceInfoCounter.js');
const dotenv = require('dotenv');
dotenv.config({ path: '.env' });

jest.mock('../../../src/api/v1/serviceInfo/model/serviceInfoModel.js');

jest.mock(
    '../../../src/api/v1/serviceInfo/counter/serviceInfoCounter.js',
    () => ({
        getNextSequence: jest.fn(),
    })
);
jest.mock('../../../src/api/v1/serviceInfo/service/serviceInfoFetcher', () => ({
    fetchAdditionalServices: jest.fn(),
    fetchServiceAvailability: jest.fn(),
    fetchLocation: jest.fn(),
    fetchGallery: jest.fn(),
    fetchFaq: jest.fn(),
    fetchSeo: jest.fn(),
    updateFieldIfChanged: jest.fn(),
    updateRelatedData: jest.fn(),
    validateLocations: jest.fn(),
}));
jest.mock('../../../src/api/v1/serviceInfo/service/relatedDataService', () => ({
    handleRelatedData: jest.fn(),
    saveAdditionalService: jest.fn(),
    saveAvailability: jest.fn(),
    saveLocations: jest.fn(),
    saveGallery: jest.fn(),
    saveFaq: jest.fn(),
    saveSEO: jest.fn(),
    deleteAdditionalService: jest.fn(),
    deleteAvailability: jest.fn(),
    deleteLocations: jest.fn(),
    deleteGallery: jest.fn(),
    deleteFaq: jest.fn(),
    deleteSEO: jest.fn(),
}));
jest.mock('../../../src/api/v1/staff/staffService', () => ({
    updateStaffServiceIds: jest.fn(),
    removeServiceIdFromStaff: jest.fn(),
}));

describe('ServiceInfoService', () => {
    let mockServiceData;
    let mockServiceId;

    beforeEach(() => {
        mockServiceId = 'SID_1';
        mockServiceData = {
            serviceTitle: 'Test Service',
            slug: 'test-service',
            categoryId: 'CAT_1',
            subCategoryId: 'SUBCAT_1',
            price: 100,
            offerPrice: 90,
            priceAfterDiscount: 80,
            duration: 60,
            allowZoomMeeting: true,
            zoomInvitationLink: 'zoomLink',
            allowGoogleMeet: true,
            googleInvitationLink: 'googleLink',
            includes: ['Includes description'],
            serviceOverview: 'Service overview description',
            isActive: true,
        };

        // Clear mocks before each test
        jest.clearAllMocks();
    });

    describe('createServiceInformation', () => {
        test('should create a service and associate related data', async () => {
            const mockService = {
                ...mockServiceData,
                serviceId: mockServiceId,
                save: jest.fn().mockResolvedValue({
                    ...mockServiceData,
                    serviceId: mockServiceId,
                }),
            };

            // Mock the sequence generator to return 1
            ServiceInfoCounter.getNextSequence.mockResolvedValue(1);

            // Mock the save method on ServiceInfo
            ServiceInfo.prototype.save = jest
                .fn()
                .mockResolvedValue(mockService);

            // Mock related data service to return mock related data
            relatedDataService.handleRelatedData.mockResolvedValue({
                additionalServicesId: ['ADD_1'],
                serviceAvailableIds: ['AVAIL_1'],
                locationIds: ['LOC_1'],
                galleryIds: ['GALLERY_1'],
                faqIds: ['FAQ_1'],
                seoIds: ['SEO_1'],
            });

            // Act: Create service information
            const result =
                await ServiceInfoService.createServiceInformation(
                    mockServiceData
                );

            // Assert that getNextSequence and save are called
            expect(ServiceInfoCounter.getNextSequence).toHaveBeenCalled();
            expect(ServiceInfo.prototype.save).toHaveBeenCalled();
            expect(StaffService.updateStaffServiceIds).toHaveBeenCalledWith(
                mockService.staff,
                mockServiceId
            );
            expect(result.serviceId).toEqual(mockServiceId);
            expect(result.serviceTitle).toEqual(mockService.serviceTitle);

            // Assert related data is correctly assigned to the result
            expect(result.additionalServicesId).toEqual(['ADD_1']);
            expect(result.serviceAvailableId).toEqual(['AVAIL_1']);
            expect(result.locationId).toEqual(['LOC_1']);
            expect(result.galleryId).toEqual(['GALLERY_1']);
            expect(result.faqId).toEqual(['FAQ_1']);
            expect(result.seoId).toEqual(['SEO_1']);
        });

        test('should throw error if creating service fails', async () => {
            const errorMessage = 'Failed to create service';
            ServiceInfoCounter.getNextSequence.mockRejectedValue(
                new Error(errorMessage)
            );

            await expect(
                ServiceInfoService.createServiceInformation(mockServiceData)
            ).rejects.toThrowError(
                `Error creating service information: ${errorMessage}`
            );
        });
    });

    describe('getServiceInformationById', () => {
        test('should return service information by ID including availability', async () => {
            const mockServiceId = 'mock-service-id';
            const mockService = {
                serviceId: mockServiceId,
                serviceTitle: 'Test Service',
                slug: 'test-service',
                categoryId: 'CAT_1',
                subCategoryId: 'SUBCAT_1',
                price: 100,
                offerPrice: 90,
                priceAfterDiscount: 80,
                duration: 60,
                allowZoomMeeting: true,
                zoomInvitationLink: 'zoomLink',
                allowGoogleMeet: true,
                googleInvitationLink: 'googleLink',
                includes: ['Includes description'],
                serviceOverview: 'Service overview description',
                isActive: true,
            };

            ServiceInfo.findOne.mockResolvedValue(mockService);

            serviceInfoFetcher.fetchServiceAvailability.mockResolvedValue([
                { day: 'Monday', slots: [] },
                { day: 'Tuesday', slots: [] },
                { day: 'Wednesday', slots: [] },
                { day: 'Thursday', slots: [] },
                { day: 'Friday', slots: [] },
                { day: 'Saturday', slots: [] },
                {
                    day: 'Sunday',
                    slots: [
                        {
                            from: '09:30',
                            to: '10:00',
                            maxBookings: 5,
                            availableSlots: 5,
                            booked: 0,
                        },
                        {
                            from: '10:30',
                            to: '13:00',
                            maxBookings: 5,
                            availableSlots: 5,
                            booked: 0,
                        },
                    ],
                },
            ]);

            // Mock other serviceInfoFetcher methods
            serviceInfoFetcher.fetchAdditionalServices.mockResolvedValue([]);
            serviceInfoFetcher.fetchLocation.mockResolvedValue([]);
            serviceInfoFetcher.fetchGallery.mockResolvedValue([]);
            serviceInfoFetcher.fetchFaq.mockResolvedValue([]);
            serviceInfoFetcher.fetchSeo.mockResolvedValue([]);

            // Act
            const result =
                await ServiceInfoService.getServiceInformationById(
                    mockServiceId
                );

            // Assert
            expect(ServiceInfo.findOne).toHaveBeenCalledWith({
                serviceId: mockServiceId,
            });
            expect(result.serviceId).toEqual(mockServiceId);
            expect(result.serviceTitle).toEqual(mockService.serviceTitle);
            expect(result.allowZoomMeeting).toEqual(
                mockService.allowZoomMeeting
            );
            expect(result.zoomInvitationLink).toEqual(
                mockService.zoomInvitationLink
            );
            expect(result.allowGoogleMeet).toEqual(mockService.allowGoogleMeet);
            expect(result.googleInvitationLink).toEqual(
                mockService.googleInvitationLink
            );
            expect(result.includes).toEqual(mockService.includes);
            expect(result.serviceOverview).toEqual(mockService.serviceOverview);
            expect(result.isActive).toEqual(mockService.isActive);

            expect(result.additionalServices).toEqual([]);
            expect(result.availability).toEqual([
                { alldate: false, day: 'Monday', slots: [] },
                { alldate: false, day: 'Tuesday', slots: [] },
                { alldate: false, day: 'Wednesday', slots: [] },
                { alldate: false, day: 'Thursday', slots: [] },
                { alldate: false, day: 'Friday', slots: [] },
                { alldate: false, day: 'Saturday', slots: [] },
                {
                    alldate: false,
                    day: 'Sunday',
                    slots: [
                        {
                            availableSlots: 5,
                            booked: 0,
                            from: '09:30',
                            maxBookings: 5,
                            to: '10:00',
                        },
                        {
                            availableSlots: 5,
                            booked: 0,
                            from: '10:30',
                            maxBookings: 5,
                            to: '13:00',
                        },
                    ],
                },
            ]);
            expect(result.location).toEqual([]);
            expect(result.gallery).toEqual([]);
            expect(result.faq).toEqual([]);
            expect(result.seo).toEqual([]);
        });

        test('should throw error if service not found', async () => {
            ServiceInfo.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                ServiceInfoService.getServiceInformationById(mockServiceId)
            ).rejects.toThrow('Service not found.');
        });
    });

    // describe('updateServiceInformation', () => {
    //     test('should update service information successfully', async () => {
    //         const serviceId = 'SID_1';
    //         const updatedData = { serviceTitle: 'Updated Test Service' };
    //         const updatedService = { ...mockServiceData, ...updatedData };

    //         ServiceInfo.findOne.mockResolvedValue(mockServiceData);
    //         ServiceInfo.findOneAndUpdate.mockResolvedValue(updatedService);

    //         serviceInfoFetcher.updateRelatedData.mockResolvedValue([]);

    //         // Act
    //         const result = await ServiceInfoService.updateServiceInformation(
    //             serviceId,
    //             updatedData
    //         );

    //         // Assert
    //         expect(ServiceInfo.findOne).toHaveBeenCalledWith({ serviceId });
    //         expect(ServiceInfo.findOneAndUpdate).toHaveBeenCalled();
    //         expect(result.serviceTitle).toEqual('Updated Test Service');
    //         expect(result).toEqual(updatedService);
    //         expect(serviceInfoFetcher.updateRelatedData).toHaveBeenCalled();
    //     });

    //     test('should throw error if service not found', async () => {
    //         const serviceId = 'SID_1';
    //         const updatedData = { serviceTitle: 'Updated Test Service' };
    //         ServiceInfo.findOne.mockResolvedValue(null);

    //         // Act & Assert
    //         await expect(
    //             ServiceInfoService.updateServiceInformation(
    //                 serviceId,
    //                 updatedData
    //             )
    //         ).rejects.toThrow('Service not found');
    //     });
    // });

    describe('deleteServiceInformation', () => {
        test('should delete service', async () => {
            const serviceId = 'SID_1';
            const deletedService = { serviceId, serviceTitle: 'Test Service' };
            ServiceInfo.findOne.mockResolvedValue({ serviceId });
            ServiceInfo.findOneAndDelete.mockResolvedValue(deletedService);

            // Act
            const result =
                await ServiceInfoService.deleteServiceInformation(serviceId);

            // Assert
            expect(ServiceInfo.findOne).toHaveBeenCalledWith({ serviceId });
            expect(ServiceInfo.findOneAndDelete).toHaveBeenCalled();
            expect(result).toEqual(deletedService);
        });

        test('should throw error if service not found for deletion', async () => {
            const serviceId = 'SID_1';
            ServiceInfo.findOne.mockResolvedValue(null);

            // Act & Assert
            await expect(
                ServiceInfoService.deleteServiceInformation(serviceId)
            ).rejects.toThrow('Service not found.');
        });
    });
});
