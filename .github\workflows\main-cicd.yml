name: CI/CD Pipeline

on:
    pull_request:
        branches:
            - main
    push:
        branches:
            - main

permissions:
    contents: write # Needed for pushing changes

jobs:
    # Lint job: Check for code quality
    # lint:
    #   runs-on: ubuntu-latest
    #   steps:
    #     - name: Checkout source code
    #       uses: actions/checkout@v4

    #     - name: Cache npm dependencies
    #       uses: actions/cache@v4
    #       with:
    #         path: ~/.npm
    #         key: ${{ runner.os }}-npm-${{ hashFiles('package-lock.json') }}
    #         restore-keys: |
    #           ${{ runner.os }}-npm-

    #     - name: Set up Node.js
    #       uses: actions/setup-node@v4
    #       with:
    #         node-version: "18"

    #     - name: Configure AWS credentials for CodeArtifact
    #       uses: aws-actions/configure-aws-credentials@v4
    #       with:
    #         aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
    #         aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    #         aws-region: us-east-1  # Use us-east-1 for CodeArtifact

    #     - name: Authenticate npm with AWS CodeArtifact
    #       run: |
    #         aws codeartifact login --tool npm \
    #           --domain aplicy \
    #           --domain-owner 339713138420 \
    #           --repository aplicy-codeartifact \
    #           --region us-east-1

    #     - name: Install dependencies
    #       run: npm install

    #     - name: Run ESLint
    #       run: npm run lint

    #     - name: Run Prettier
    #       run: npm run format

    #     - name: Upload Lint Report
    #       if: always()
    #       uses: actions/upload-artifact@v4
    #       with:
    #         name: eslint-report
    #         path: eslint-report.html

    #     - name: Temporarily Use npmjs.org for Security Audit
    #       run: npm config set registry https://registry.npmjs.org/

    #     - name: Run Security Audit (npm audit)
    #       run: npm audit --audit-level=high || true  # Do not fail CI if issues exist

    #     - name: Restore AWS CodeArtifact Registry
    #       run: |
    #         aws codeartifact login --tool npm \
    #           --domain aplicy \
    #           --domain-owner 339713138420 \
    #           --repository aplicy-codeartifact \
    #           --region us-east-1

    #     - name: Run Dependency Check (depcheck)
    #       run: |
    #         npm install -g depcheck
    #         depcheck > depcheck-report.txt || true  # Save output to a file

    #     - name: Upload Depcheck Report
    #       uses: actions/upload-artifact@v4
    #       with:
    #         name: depcheck-report
    #         path: depcheck-report.txt

    # Test job: Run unit tests
    # test:
    #   needs: lint
    #   runs-on: ubuntu-latest
    #   steps:
    #     - name: Checkout source code
    #       uses: actions/checkout@v4

    #     - name: Restore npm cache
    #       uses: actions/cache@v4
    #       with:
    #         path: ~/.npm
    #         key: ${{ runner.os }}-npm-${{ hashFiles('package-lock.json') }}
    #         restore-keys: |
    #           ${{ runner.os }}-npm-

    #     - name: Set up Node.js
    #       uses: actions/setup-node@v4
    #       with:
    #         node-version: "18"

    #     - name: Configure AWS credentials for CodeArtifact
    #       uses: aws-actions/configure-aws-credentials@v4
    #       with:
    #         aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
    #         aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    #         aws-region: us-east-1

    #     - name: Authenticate npm with AWS CodeArtifact
    #       run: |
    #         aws codeartifact login --tool npm \
    #           --domain aplicy \
    #           --domain-owner 339713138420 \
    #           --repository aplicy-codeartifact \
    #           --region us-east-1

    #     - name: Install dependencies (including gigmosaic-common-libs)
    #       run: npm install

    #     - name: Set Environment Variables
    #       run: |
    #         echo "NODE_ENV=test" >> $GITHUB_ENV
    #         echo "CI=true" >> $GITHUB_ENV
    #         echo "ELASTIC_API_KEY=${{ secrets.ELASTIC_API_KEY }}" >> $GITHUB_ENV
    #         echo "ELASTIC_NODE=${{ secrets.ELASTIC_NODE }}" >> $GITHUB_ENV

    #     - name: Run Tests
    #       run: npm test -- --coverage

    #     - name: Upload Coverage Report
    #       if: always()
    #       uses: actions/upload-artifact@v4
    #       with:
    #         name: coverage-report
    #         path: coverage/

    # # Security scan: Run Trivy
    # security-scan:
    #   needs: test
    #   runs-on: ubuntu-latest
    #   steps:
    #     - name: Checkout source code
    #       uses: actions/checkout@v4

    #     - name: Run Trivy for Dependency Scanning
    #       uses: aquasecurity/trivy-action@0.14.0
    #       with:
    #         scan-type: "fs"
    #         ignore-unfixed: true
    #         format: "table"
    #         exit-code: 1
    #         severity: "CRITICAL,HIGH"

    # # API Security Scan: Run OWASP ZAP (Optional)
    # api-security-scan:
    #   needs: test
    #   runs-on: ubuntu-latest
    #   steps:
    #     - name: Checkout source code
    #       uses: actions/checkout@v4

    #     - name: Verify OpenAPI Spec File Exists
    #       run: |
    #         if [ ! -f "./src/OpenAPI.yaml" ]; then
    #           echo "❌ OpenAPI.yaml file not found in src/ folder!"
    #           exit 1
    #         fi

    # ----------------------------------- Dont remove START -----------------------------------
    #      - name: Run OWASP ZAP OpenAPI Scan
    #        uses: zaproxy/action-api-scan@v0.7.0
    #        with:
    #          target: "http://localhost:3000"
    #          openapi: "./src/OpenAPI.yaml"  # FIXED: Updated the correct path
    #          format: "openapi"  # Must be 'openapi', 'soap', or 'graphql'
    #          fail_action: true
    #
    #      - name: Upload OWASP ZAP Report
    #        if: always()
    #        uses: actions/upload-artifact@v4
    #        with:
    #          name: zap-api-security-report
    #          path: report.html
    # ----------------------------------- Dont remove END -----------------------------------
    # Build and Push to AWS ECR
    build:
        # needs: test
        runs-on: ubuntu-latest
        steps:
            - name: Checkout source code
              uses: actions/checkout@v4

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: ca-central-1

            - name: Get Version from `package.json`
              id: version
              run: echo "VERSION=$(jq -r .version package.json)" >> $GITHUB_ENV

            - name: Cache Docker Layers
              uses: actions/cache@v4
              with:
                  path: /tmp/.buildx-cache
                  key: ${{ runner.os }}-docker-${{ github.sha }}
                  restore-keys: |
                      ${{ runner.os }}-docker-

            - name: Authenticate Docker to AWS ECR
              run: |
                  aws ecr get-login-password --region ca-central-1 | docker login --username AWS --password-stdin 339713138420.dkr.ecr.ca-central-1.amazonaws.com

            - name: Configure AWS Credentials
              uses: aws-actions/configure-aws-credentials@v2
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: us-east-1 # Adjust region if needed

            - name: Get CodeArtifact Auth Token
              run: |
                  echo "CODEARTIFACT_AUTH_TOKEN=$(aws codeartifact get-authorization-token --domain aplicy --query authorizationToken --output text --region us-east-1)" >> $GITHUB_ENV

            - name: Build and Tag Docker Image
              run: |
                  echo "Building Docker Image..."
                  docker build --build-arg CODEARTIFACT_AUTH_TOKEN=$CODEARTIFACT_AUTH_TOKEN -t aplicy/gigmosaic-product-mgmt-be:$VERSION .
                  docker tag aplicy/gigmosaic-product-mgmt-be:$VERSION 339713138420.dkr.ecr.ca-central-1.amazonaws.com/aplicy/gigmosaic-product-mgmt-be:$VERSION
                  docker images
                  docker images

            - name: Push Docker Image to AWS ECR
              run: |
                  docker push 339713138420.dkr.ecr.ca-central-1.amazonaws.com/aplicy/gigmosaic-product-mgmt-be:$VERSION

    # Deploy to Self-Hosted Server
    deploy:
        needs: build
        runs-on: self-hosted
        steps:
            - name: Free Disk Space Before Deployment
              run: |
                  echo "🧹 Cleaning up space before Docker pull..."

                  # Clean npm cache to reclaim space
                  sudo npm cache clean --force || true

                  # Prune unused Docker data
                  sudo docker system prune -af || true

                  # Remove dangling volumes (optional)
                  sudo docker volume prune -f || true

                  # Show disk space after cleanup
                  df -h
            - name: SSH into Server and Deploy
              env:
                  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  AWS_DEFAULT_REGION: ca-central-1
              run: |
                  cd /home/<USER>/devops/gigmosaic

                  # Authenticate to AWS ECR
                  aws ecr get-login-password --region ca-central-1 | docker login --username AWS --password-stdin 339713138420.dkr.ecr.ca-central-1.amazonaws.com


                  # Pull the latest Docker image from ECR
                  docker pull 339713138420.dkr.ecr.ca-central-1.amazonaws.com/aplicy/gigmosaic-product-mgmt-be:latest

                  # Stop and remove old containers
                  sudo docker-compose down || true
                  sudo docker-compose up -d --force-recreate --remove-orphans

                  # Verify all running containers
                  docker ps --format "table {{.Names}}\t{{.Status}}"

            - name: Verify container is running
              run: |
                  CONTAINER_NAME="gigmosaic-product-mgmt-service-container"
                  sleep 5
                  if docker ps --filter "name=$CONTAINER_NAME" --format "{{.Names}}" | grep -q "$CONTAINER_NAME"; then
                    echo "✅ Container '$CONTAINER_NAME' is running successfully."
                  else
                    echo "❌ Error: Container '$CONTAINER_NAME' is not running."
                    echo "🔍 Fetching container logs..."
                    docker logs $CONTAINER_NAME
                    exit 1
                  fi

            - name: Clean up unused Docker images and containers
              run: sudo docker system prune -af

    # Notify on Completion
    notify:
        needs: deploy
        runs-on: ubuntu-latest
        steps:
            - name: Send notification to Google Chat
              run: |
                  echo "App deployed and running successfully."

    update-version:
        if: github.ref == 'refs/heads/main' # Run only on 'main' branch
        needs: deploy
        runs-on: ubuntu-latest
        permissions:
            contents: write # Allow push access

        steps:
            - name: Checkout Repository
              uses: actions/checkout@v4
              with:
                  token: ${{ secrets.ACCESS_GITHUB_TOKEN }}

            - name: Set Up Git User
              run: |
                  git config --global user.email "<EMAIL>"
                  git config --global user.name "GitHub Actions CI"

            - name: Bump Version in `package.json`
              run: |
                  git fetch --prune --unshallow
                  npm version patch -m "🔄 CI: Bump version to %s [skip ci]"
                  git push origin main --follow-tags
