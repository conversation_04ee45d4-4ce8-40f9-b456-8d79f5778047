/* eslint-disable no-undef */
const request = require('supertest');
const app = require('../../../src/app');
const { setupDB, teardownDB } = require('../../environment/setupTest');
const { SUBCATEGORY_URL } = require('../../urls');
let subCategoryId;

beforeAll(async () => {
    await setupDB();
});

afterAll(async () => {
    await teardownDB();
});

describe('SubCategory API', () => {
    it('should create a new subcategory', async () => {
        const subCategoryData = {
            subCategoryName: 'Test SubCategory',
            subCategorySlug: 'test-subcategory',
            categoryId: 'category123',
        };

        const response = await request(app)
            .post(SUBCATEGORY_URL)
            .send(subCategoryData);

        expect(response.status).toBe(201);
        expect(response.body.success).toBe(true);
        expect(response.body.subCategory).toHaveProperty('subCategoryId');

        subCategoryId = response.body.subCategory.subCategoryId;
    });

    it('should fetch the created subcategory by ID', async () => {
        const response = await request(app)
            .get(`${SUBCATEGORY_URL}/${subCategoryId}`)
            .send();

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.subCategory).toHaveProperty(
            'subCategoryId',
            subCategoryId
        );
    });

    it('should return 404 if subcategory not found', async () => {
        const response = await request(app)
            .get(`${SUBCATEGORY_URL}/999999`)
            .send();

        expect(response.status).toBe(404);
        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual([
            {
                field: 'subCategoryId',
                message: 'SubCategory not found',
            },
        ]);
        expect(response.body.message).toMatch(
            /.*The requested resource could not be found. Please check the URL and try again.*/
        );
    });

    it('should update an existing subcategory', async () => {
        const updatedData = {
            subCategoryName: 'Updated Test SubCategory',
            subCategorySlug: 'updated-test-subcategory',
        };

        const response = await request(app)
            .put(`${SUBCATEGORY_URL}/${subCategoryId}`)
            .send(updatedData);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.subCategory.subCategoryName).toBe(
            updatedData.subCategoryName
        );
    });

    it('should return 404 if subcategory to update is not found', async () => {
        const response = await request(app)
            .put(`${SUBCATEGORY_URL}/999999`)
            .send({ subCategoryName: 'Non-existent SubCategory' });

        expect(response.status).toBe(404);
        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual([
            {
                field: 'subCategoryId',
                message: 'SubCategory not found for update',
            },
        ]);
        expect(response.body.message).toMatch(
            /.*The requested resource could not be found. Please check the URL and try again.*/
        );
    });

    it('should delete the subcategory', async () => {
        const response = await request(app)
            .delete(`${SUBCATEGORY_URL}/${subCategoryId}`)
            .send();

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.message).toBe('SubCategory deleted successfully');
    });

    it('should return 404 if subcategory to delete is not found', async () => {
        const response = await request(app)
            .delete(`${SUBCATEGORY_URL}/999999`)
            .send();

        expect(response.status).toBe(404);
        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual([
            {
                field: 'subCategoryId',
                message: 'SubCategory not found for deletion',
            },
        ]);
        expect(response.body.message).toMatch(
            /.*The requested resource could not be found. Please check the URL and try again.*/
        );
    });
});
