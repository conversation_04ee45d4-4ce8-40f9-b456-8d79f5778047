const AdditionalService = require('../model/additionalServiceModel');
const Gallery = require('../model/galleryModel');
const Location = require('../model/locationModel');
const Seo = require('../model/seoModel');
const ServiceAvailability = require('../model/serviceAvailabilityModel');
const Faq = require('../model/faqModel');

const AdditionalServiceCounter = require('../counter/additionalServiceCounter');
const FaqCounter = require('../counter/faqCounter');
const GalleryCounter = require('../counter/galleryCounter');
const LocationCounter = require('../counter/locationCounter');
const SeoCounter = require('../counter/seoCounter');
const ServiceAvailabilityCounter = require('../counter/serviceAvailabilityCounter');
const logger = require('../../../common/utils/logger');
/**
 * Helper function to save related data
 * @param {Array} items - The array of items to save
 * @param {String} serviceId - The service ID
 * @param {Model} model - The model to use for saving documents
 * @param {Object} counter - The counter for generating unique IDs
 * @param {String} idPrefix - The prefix for generated IDs
 * @param {String} idFieldName - The field name for storing the generated ID
 * @returns {Promise<Array>} - Array of generated IDs
 */
const saveRelatedData = async (
    items = [],
    serviceId,
    model,
    counter,
    idPrefix,
    idFieldName
) => {
    try {
        const savedIds = await Promise.all(
            (Array.isArray(items) ? items : []).map(async (item) => {
                const newId = await counter.getNextSequence();
                const generatedId = `${idPrefix}_${newId}`;

                const documentData = {
                    [idFieldName]: generatedId,
                    serviceId,
                    ...item,
                };

                const document = new model(documentData);
                await document.save();
                logger.info(`Saved ${model.modelName} with ID: ${generatedId}`);
                return generatedId;
            })
        );
        return savedIds;
    } catch (error) {
        logger.error(`Error saving related data: ${error.message}`);
        throw new Error('Error saving related data');
    }
};

const saveAdditionalService = async (additionalService = [], serviceId) => {
    return saveRelatedData(
        additionalService,
        serviceId,
        AdditionalService,
        AdditionalServiceCounter,
        'ASID',
        'additionalServiceId'
    );
};

const saveAvailability = async (availability, serviceId) => {
    let processedAvailability;

    const isAllDateAvailable = availability.some(
        (item) => item.day === 'all-date' && item.available === true
    );

    if (isAllDateAvailable) {
        processedAvailability = await handleAllDateAvailability(
            availability,
            serviceId
        );
    } else {
        processedAvailability = availability.map((item) => ({
            ...item,
            serviceId,
        }));
    }

    return saveRelatedData(
        processedAvailability,
        serviceId,
        ServiceAvailability,
        ServiceAvailabilityCounter,
        'SAID',
        'serviceAvailableId'
    );
};

const handleAllDateAvailability = async (availability, serviceId) => {
    const processedAvailability = [];

    for (const item of availability) {
        if (item.day === 'all-date' && item.available === true) {
            const daysOfWeek = [
                'monday',
                'tuesday',
                'wednesday',
                'thursday',
                'friday',
                'saturday',
                'sunday',
            ];

            daysOfWeek.forEach((day) => {
                const newItem = { ...item, day, serviceId };
                processedAvailability.push(newItem);
            });
        } else {
            processedAvailability.push({ ...item, serviceId });
        }
    }

    return processedAvailability;
};

const saveLocations = async (locations = [], serviceId) => {
    return saveRelatedData(
        locations,
        serviceId,
        Location,
        LocationCounter,
        'LID',
        'locationId'
    );
};

const saveGallery = async (galleryItems = [], serviceId) => {
    return saveRelatedData(
        galleryItems,
        serviceId,
        Gallery,
        GalleryCounter,
        'GID',
        'galleryId'
    );
};

const saveFaq = async (faqItems = [], serviceId) => {
    return saveRelatedData(
        faqItems,
        serviceId,
        Faq,
        FaqCounter,
        'FAQID',
        'faqId'
    );
};

const saveSEO = async (seoItems = [], serviceId) => {
    return saveRelatedData(
        seoItems,
        serviceId,
        Seo,
        SeoCounter,
        'SEOID',
        'seoId'
    );
};

const handleRelatedData = async (serviceId, relatedData) => {
    const relatedDataPromises = [
        saveAdditionalService(relatedData.additionalService, serviceId),
        saveAvailability(relatedData.availability, serviceId),
        saveLocations(relatedData.location, serviceId),
        saveGallery(relatedData.gallery, serviceId),
        saveFaq(relatedData.faq, serviceId),
        saveSEO(relatedData.seo, serviceId),
    ];

    try {
        const [
            additionalServicesId,
            serviceAvailableIds,
            locationIds,
            galleryIds,
            faqIds,
            seoIds,
        ] = await Promise.all(relatedDataPromises);

        return {
            additionalServicesId,
            serviceAvailableIds,
            locationIds,
            galleryIds,
            faqIds,
            seoIds,
        };
    } catch (error) {
        logger.error(`Error handling related data: ${error.message}`);
        throw new Error('Error handling related data.');
    }
};

const deleteRelatedData = async (model, columnName, ids) => {
    try {
        if (!Array.isArray(ids)) {
            throw new Error('IDs must be an array.');
        }

        const result = await model.deleteMany({
            [columnName]: { $in: ids },
        });

        if (result.deletedCount > 0) {
            logger.info(
                `${columnName}: ${ids}: Successfully deleted ${result.deletedCount} record(s).`
            );
        } else {
            logger.warn(`${columnName}: ${ids}: No records were deleted.`);
        }

        return result;
    } catch (error) {
        logger.error(`Error deleting records: ${error.message}`);
        throw new Error('Error deleting records.');
    }
};

const deleteAdditionalService = async (additionalServicesIds) => {
    return await deleteRelatedData(
        AdditionalService,
        'additionalServiceId',
        additionalServicesIds
    );
};

const deleteAvailability = async (availabilityIds) => {
    return await deleteRelatedData(
        ServiceAvailability,
        'serviceAvailableId',
        availabilityIds
    );
};

const deleteLocations = async (locationIds) => {
    return await deleteRelatedData(Location, 'locationId', locationIds);
};

const deleteGallery = async (galleryIds) => {
    return await deleteRelatedData(Gallery, 'galleryId', galleryIds);
};

const deleteFaq = async (faqIds) => {
    return await deleteRelatedData(Faq, 'faqId', faqIds);
};

const deleteSEO = async (seoIds) => {
    return await deleteRelatedData(Seo, 'seoId', seoIds);
};

module.exports = {
    saveAdditionalService,
    saveAvailability,
    saveLocations,
    saveGallery,
    saveFaq,
    saveSEO,
    deleteAdditionalService,
    deleteAvailability,
    deleteLocations,
    deleteGallery,
    deleteFaq,
    deleteSEO,
    handleRelatedData,
};
