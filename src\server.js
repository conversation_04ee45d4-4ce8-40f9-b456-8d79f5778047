/* eslint-disable id-length */
const http = require('http');

const socketIo = require('socket.io');

const app = require('./app');

const { connectToDatabase } = require('./api/common/config/db');

const logger = require('./api/common/utils/logger');

connectToDatabase();

const server = http.createServer(app);

const io = socketIo(server);

io.on('connection', (socket) => {
    logger.info('A user connected');

    socket.on('message', (data) => {
        logger.info('Received message: ' + data);

        socket.emit('response', 'Message received successfully!');
    });

    socket.on('disconnect', () => {
        logger.info('A user disconnected');
    });
});

const port = process.env.PORT || 3000;

server.listen(port, () => {
    logger.info(`Server is running on port ${port}`);
});
