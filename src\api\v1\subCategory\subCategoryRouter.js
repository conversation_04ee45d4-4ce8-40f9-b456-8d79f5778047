const express = require('express');

const router = express.Router();

const SubCategoryController = require('./subCategoryController');

const { validateSubCategory } = require('./subCategoryMiddleware');

const fetchAuth = require('../../common/utils/communicator');

router.post(
    '/',
    validateSubCategory,
    fetchAuth.fetchAuthAdminDataMiddleware,
    SubCategoryController.createSubCategory
);

router.get('/', SubCategoryController.getSubCategory);

router.get('/:subCategoryId', SubCategoryController.getSubCategoryById);

router.put(
    '/:subCategoryId',
    validateSubCategory,
    fetchAuth.fetchAuthAdminDataMiddleware,
    SubCategoryController.updateSubCategory
);

router.delete(
    '/:subCategoryId',
    fetchAuth.fetchAuthAdminDataMiddleware,
    SubCategoryController.deleteSubCategory
);

module.exports = router;
