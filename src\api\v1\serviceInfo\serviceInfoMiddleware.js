/* eslint-disable consistent-return */
const { body, check, validationResult } = require('express-validator');

const validateServiceInformation = [
    body('serviceTitle')
        .isString()
        .withMessage('Service title must be a string')
        .notEmpty()
        .withMessage('Service title is required'),

    body('slug')
        .isString()
        .withMessage('Slug must be a string')
        .notEmpty()
        .withMessage('Slug is required'),

    body('categoryId')
        .isString()
        .withMessage('Category ID must be a string')
        .notEmpty()
        .withMessage('Category ID is required'),

    body('subCategoryId')
        .isString()
        .withMessage('Sub-category ID must be a string')
        .notEmpty()
        .withMessage('Sub-category ID is required'),

    body('price')
        .isInt({ min: 0 })
        .withMessage('Price must be a valid number greater than or equal to 0'),

    body('offerPrice')
        .isInt({ min: 0 })
        .withMessage(
            'Offer price must be a valid number greater than or equal to 0'
        ),

    body('priceAfterDiscount')
        .isInt({ min: 0 })
        .withMessage(
            'Price after discount must be a valid number greater than or equal to 0'
        ),

    body('duration')
        .isString()
        .withMessage('Duration must be a string')
        .optional()
        .withMessage('Duration is optional'),

    body('staff')
        .isString()
        .withMessage('staff must be a string')
        .optional()
        .withMessage('staff is optional'),

    body('includes')
        .isString()
        .withMessage('includes must be a string')
        .optional()
        .withMessage('includes is optional'),

    body('serviceOverview')
        .isString()
        .withMessage('serviceOverview must be a string')
        .notEmpty()
        .withMessage('serviceOverview is optional'),

    body('isActive')
        .isBoolean()
        .withMessage('isActive must be a boolean value'),

    body('isAdditional')
        .isBoolean()
        .withMessage('isAdditional must be a boolean value'),

    check('additionalService')
        .optional()
        .isArray({ min: 1 })
        .withMessage(
            'additionalService must be an array with at least one item if provided.'
        )
        .custom((value) => {
            if (value) {
                value.forEach((item) => {
                    if (
                        !item.serviceItem ||
                        typeof item.serviceItem !== 'string'
                    ) {
                        throw new Error('serviceItem must be a string.');
                    }
                    if (typeof item.price !== 'number' || item.price <= 0) {
                        throw new Error('price must be a positive number.');
                    }
                    if (typeof item.images !== 'string') {
                        throw new Error('images must be a string.');
                    }
                });
            }
            return true;
        }),

    check('availability')
        .optional()
        .isArray({ min: 1 })
        .withMessage(
            'availability must be an array with at least one item if provided.'
        )
        .custom((value) => {
            if (value) {
                value.forEach((item) => {
                    if (
                        !item.day ||
                        ![
                            'monday',
                            'tuesday',
                            'wednesday',
                            'thursday',
                            'friday',
                            'saturday',
                            'sunday',
                            ' all-date',
                        ].includes(item.day)
                    ) {
                        throw new Error(
                            'day must be one of the following: monday, tuesday, wednesday, thursday, friday, saturday, sunday.'
                        );
                    }
                    if (typeof item.available !== 'boolean') {
                        throw new Error('available must be a boolean.');
                    }
                    if (
                        !item.timeSlots ||
                        !Array.isArray(item.timeSlots) ||
                        item.timeSlots.length === 0
                    ) {
                        throw new Error('timeSlots must be a non-empty array.');
                    }

                    item.timeSlots.forEach((slot) => {
                        if (!slot.from || !slot.to || !slot.maxBookings) {
                            throw new Error(
                                'Each time slot must have from, to, and maxBookings fields.'
                            );
                        }
                        if (
                            !/^([01]?[0-9]|2[0-3]):([0-5][0-9]) (\s?[APap][Mm])$/.test(
                                slot.from
                            ) ||
                            !/^([01]?[0-9]|2[0-3]):([0-5][0-9])(\s?[APap][Mm])$/.test(
                                slot.to
                            )
                        ) {
                            throw new Error(
                                'Time must be in valid HH:mm format.'
                            );
                        }
                        if (
                            typeof slot.maxBookings !== 'number' ||
                            slot.maxBookings < 1
                        ) {
                            throw new Error(
                                'maxBookings must be a number greater than or equal to 1.'
                            );
                        }
                    });
                });
            }
            return true;
        }),

    check('location')
        .optional()
        .isArray({ min: 1 })
        .withMessage(
            'location must be an array with at least one item if provided.'
        )
        .custom((value) => {
            if (value) {
                value.forEach((item) => {
                    if (!item.address || typeof item.address !== 'string') {
                        throw new Error('address must be a string.');
                    }
                    if (!item.city || typeof item.city !== 'string') {
                        throw new Error('city must be a string.');
                    }
                    if (!item.state || typeof item.state !== 'string') {
                        throw new Error('state must be a string.');
                    }
                    if (!item.country || typeof item.country !== 'string') {
                        throw new Error('country must be a string.');
                    }
                    if (!item.pinCode || typeof item.pinCode !== 'string') {
                        throw new Error(
                            'pinCode must be a valid 5 or 6 digit number.'
                        );
                    }
                    if (
                        typeof item.longitude !== 'number' ||
                        typeof item.latitude !== 'number'
                    ) {
                        throw new Error(
                            'longitude and latitude must be numbers.'
                        );
                    }
                });
            }
            return true;
        }),

    check('gallery')
        .optional()
        .isArray({ min: 1 })
        .withMessage(
            'gallery must be an array with at least one item if provided.'
        )
        .custom((value) => {
            if (value) {
                value.forEach((item) => {
                    if (
                        item.serviceImages &&
                        Array.isArray(item.serviceImages)
                    ) {
                        item.serviceImages.forEach((image) => {
                            if (
                                typeof image !== 'string' ||
                                !/^https?:\/\/[^\s]+$/.test(image)
                            ) {
                                throw new Error(
                                    'serviceImages must be an array of valid URLs.'
                                );
                            }
                        });
                    }
                    if (item.serviceVideo && Array.isArray(item.serviceVideo)) {
                        item.serviceVideo.forEach((video) => {
                            if (
                                typeof video !== 'string' ||
                                !/^https?:\/\/[^\s]+$/.test(video)
                            ) {
                                throw new Error(
                                    'serviceVideo must be an array of valid URLs.'
                                );
                            }
                        });
                    }
                    if (item.videoLink && typeof item.videoLink !== 'string') {
                        throw new Error('videoLink must be a valid URL.');
                    }
                });
            }
            return true;
        }),

    check('faq')
        .optional()
        .isArray({ min: 1 })
        .withMessage('faq must be an array with at least one item if provided.')
        .custom((value) => {
            if (value) {
                value.forEach((item) => {
                    if (!item.question || typeof item.question !== 'string') {
                        throw new Error(
                            'Each FAQ must have a question field that is a string.'
                        );
                    }
                    if (!item.answer || typeof item.answer !== 'string') {
                        throw new Error(
                            'Each FAQ must have an answer field that is a string.'
                        );
                    }
                });
            }
            return true;
        }),

    check('seo')
        .optional()
        .isArray({ min: 1 })
        .withMessage('seo must be an array with at least one item if provided.')
        .custom((value) => {
            if (value) {
                value.forEach((item) => {
                    if (
                        !item.metaTitle ||
                        typeof item.metaTitle !== 'string' ||
                        item.metaTitle.length > 150
                    ) {
                        throw new Error(
                            'metaTitle must be a string with a maximum length of 150 characters.'
                        );
                    }
                    if (
                        !item.metaKeywords ||
                        !Array.isArray(item.metaKeywords)
                    ) {
                        throw new Error(
                            'metaKeywords must be an array of strings.'
                        );
                    }
                    if (
                        !item.metaDescription ||
                        typeof item.metaDescription !== 'string' ||
                        item.metaDescription.length > 160
                    ) {
                        throw new Error(
                            'metaDescription must be a string with a maximum length of 160 characters.'
                        );
                    }
                });
            }
            return true;
        }),

    (req, res, next) => {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        next();
    },
];

function routeIdentifier(type) {
    return function (req, res, next) {
        req.routeType = type;
        next();
    };
}

module.exports = {
    validateServiceInformation,
    routeIdentifier,
};
