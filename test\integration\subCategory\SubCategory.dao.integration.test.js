/* eslint-disable no-undef */
const { setupDB, teardownDB } = require('../../environment/setupTest');
const SubCategory = require('../../../src/api/v1/subCategory/subCategoryModel');
const SubCategoryService = require('../../../src/api/v1/subCategory/subCategoryService');

const subCategoryData = {
    subCategoryName: 'Test SubCategory',
    subCategorySlug: 'test-subcategory',
    categoryId: 'category123',
};

let subCategoryId;

beforeAll(async () => {
    await setupDB();
});

afterAll(async () => {
    await teardownDB();
});

describe('SubCategory DAO Integration Tests', () => {
    it('should create a new subcategory', async () => {
        const response =
            await SubCategoryService.createSubCategory(subCategoryData);

        expect(response).toHaveProperty('subCategoryId');
        expect(response.subCategoryName).toBe(subCategoryData.subCategoryName);
        expect(response.subCategorySlug).toBe(subCategoryData.subCategorySlug);

        subCategoryId = response.subCategoryId;
    });

    it('should fetch a subcategory by ID', async () => {
        const subCategory =
            await SubCategoryService.getSubCategoryById(subCategoryId);

        expect(subCategory).toBeDefined();
        expect(subCategory.subCategoryId).toBe(subCategoryId);
    });

    it('should return null if subcategory does not exist', async () => {
        const nonExistentSubCategory =
            await SubCategoryService.getSubCategoryById('SCID_nonexistent');

        expect(nonExistentSubCategory).toBeNull();
    });

    it('should update a subcategory', async () => {
        const updatedData = {
            subCategoryName: 'Updated SubCategory Name',
            subCategorySlug: 'updated-slug',
        };

        const updatedSubCategory = await SubCategoryService.updateSubCategory(
            subCategoryId,
            updatedData
        );

        expect(updatedSubCategory).toBeDefined();
        expect(updatedSubCategory.subCategoryName).toBe(
            updatedData.subCategoryName
        );
        expect(updatedSubCategory.subCategorySlug).toBe(
            updatedData.subCategorySlug
        );
    });

    it('should throw an error when updating a non-existent subcategory', async () => {
        await expect(
            SubCategoryService.updateSubCategory('SCID_nonexistent', {
                subCategoryName: 'Non Existent SubCategory',
            })
        ).rejects.toThrow('SubCategory not found');
    });

    it('should delete a subcategory', async () => {
        const deletedSubCategory =
            await SubCategoryService.deleteSubCategory(subCategoryId);

        expect(deletedSubCategory).toBeDefined();
        expect(deletedSubCategory.subCategoryId).toBe(subCategoryId);

        const subCategory = await SubCategory.findOne({ subCategoryId });
        expect(subCategory).toBeNull();
    });

    it('should throw an error when deleting a non-existent subcategory', async () => {
        await expect(
            SubCategoryService.deleteSubCategory('SCID_nonexistent')
        ).rejects.toThrow('SubCategory not found');
    });

    it('should count the number of subcategories', async () => {
        const countBefore = await SubCategory.countDocuments();
        await SubCategoryService.createSubCategory(subCategoryData);
        const countAfter = await SubCategory.countDocuments();

        expect(countAfter).toBe(countBefore + 1);
    });
});
