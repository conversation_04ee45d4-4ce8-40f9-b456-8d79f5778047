/* eslint-disable no-undef */
const request = require('supertest');
const app = require('../../../src/app');
const { setupDB, teardownDB } = require('../../environment/setupTest');
const { CATEGORY_URL } = require('../../urls');
beforeAll(async () => {
    await setupDB();
});

afterAll(async () => {
    await teardownDB();
});

describe('Category API', () => {
    let categoryId;

    it('should create a new category', async () => {
        const categoryData = {
            categoryName: 'Test Category',
            categorySlug: 'test-category',
        };

        const response = await request(app)
            .post(CATEGORY_URL)
            .send(categoryData);

        expect(response.status).toBe(201);
        expect(response.body.success).toBe(true);
        expect(response.body.category).toHaveProperty('categoryId');
        categoryId = response.body.category.categoryId;
    });

    it('should fetch the created category by ID', async () => {
        const response = await request(app).get(
            `${CATEGORY_URL}/${categoryId}`
        );

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.category).toHaveProperty('categoryId', categoryId);
    });

    it('should fetch all categories', async () => {
        const response = await request(app).get(CATEGORY_URL);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(Array.isArray(response.body.categories)).toBe(true);
        expect(response.body.categories.length).toBeGreaterThan(0);
    });

    it('should update an existing category', async () => {
        const updatedCategoryData = {
            categoryName: 'Updated Test Category',
        };

        const response = await request(app)
            .put(`${CATEGORY_URL}/${categoryId}`)
            .send(updatedCategoryData);

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.category.categoryName).toBe(
            updatedCategoryData.categoryName
        );
    });

    it('should delete the category', async () => {
        const response = await request(app).delete(
            `${CATEGORY_URL}/${categoryId}`
        );

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.message).toBe('Category deleted successfully');
    });

    it('should return 404 for a non-existent category', async () => {
        const nonExistentCategoryId = 'non-existent-id';

        const response = await request(app).get(
            `${CATEGORY_URL}/${nonExistentCategoryId}`
        );

        expect(response.status).toBe(404);
        expect(response.body.success).toBe(false);
        expect(response.body.errors).toEqual([
            { field: 'categoryId', message: 'Category not found' },
        ]);
        expect(response.body.message).toMatch(
            /.*The requested resource could not be found. Please check the URL and try again.*/
        );
    });
});
