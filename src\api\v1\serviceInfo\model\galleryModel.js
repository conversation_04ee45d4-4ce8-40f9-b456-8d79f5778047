const mongoose = require('mongoose');

const videoLinkRegex =
    /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be|vimeo\.com|dailymotion\.com|twitch\.tv|facebook\.com|fb\.watch|instagram\.com|tiktok\.com)\/.*/;

const gallerySchema = new mongoose.Schema(
    {
        galleryId: {
            type: String,
            required: true,
        },

        serviceId: {
            type: String,
            required: true,
        },

        serviceImages: {
            type: [String],
            required: true,
        },

        serviceVideo: {
            type: [String],
        },

        videoLink: {
            type: String,
            required: false,
            validate: {
                validator: function (value) {
                    return !value || videoLinkRegex.test(value);
                },
                message: (props) =>
                    `${props.value} is not a valid video URL from an allowed platform.`,
            },
        },
    },

    {
        timestamps: true,
    }
);

module.exports = mongoose.model('Gallery', gallerySchema);
