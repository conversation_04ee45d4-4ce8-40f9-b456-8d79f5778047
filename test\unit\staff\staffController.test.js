/* eslint-disable no-undef */
const {
    createStaff,
    getStaff,
    getStaffById,
    updateStaff,
    deleteStaff,
} = require('../../../src/api/v1/staff/staffController');

const StaffService = require('../../../src/api/v1/staff/staffService');

jest.mock('../../../src/api/v1/staff/staffService');

jest.mock('express-oauth2-jwt-bearer', () => ({
    InsufficientScopeError: jest.fn(),
}));

describe('Staff Controller', () => {
    let req, res;

    // Setup for each test
    beforeEach(() => {
        req = { body: {}, params: {}, query: {} };
        res = {
            status: jest.fn().mockReturnThis(),
            json: jest.fn().mockReturnThis(),
            warn: jest.fn().mockReturnThis(),
        };

        jest.clearAllMocks(); // Clears mock calls between tests
    });

    // Mocking service method responses
    const mockServiceMethods = () => {
        StaffService.createStaff.mockResolvedValue({
            sid: 1,
            fullName: 'John Doe',
            email: '<EMAIL>',
            phoneNumber: '1234567890',
            address: '123 Main St',
            country: 'USA',
            state: 'California',
            city: 'Los Angeles',
            zipCode: '90001',
            description: 'Staff member in charge of operations.',
            serviceIds: ['SERVICE1', 'SERVICE2'],
            status: true,
        });
        StaffService.getStaff.mockResolvedValue({
            staffList: [
                {
                    sid: 1,
                    fullName: 'John Doe',
                    email: '<EMAIL>',
                    phoneNumber: '1234567890',
                    address: '123 Main St',
                    country: 'USA',
                    state: 'California',
                    city: 'Los Angeles',
                    zipCode: '90001',
                    description: 'Staff member in charge of operations.',
                    serviceIds: ['SERVICE1', 'SERVICE2'],
                    status: true,
                },
            ],
            total: 1,
        });
        StaffService.getStaffById.mockResolvedValue({
            sid: 1,
            fullName: 'John Doe',
            email: '<EMAIL>',
            phoneNumber: '1234567890',
            address: '123 Main St',
            country: 'USA',
            state: 'California',
            city: 'Los Angeles',
            zipCode: '90001',
            description: 'Staff member in charge of operations.',
            serviceIds: ['SERVICE1', 'SERVICE2'],
            status: true,
        });
        StaffService.updateStaff.mockResolvedValue({
            sid: 1,
            fullName: 'John Doe Updated',
            email: '<EMAIL>',
            phoneNumber: '0987654321',
            address: '456 Another St',
            country: 'USA',
            state: 'California',
            city: 'Los Angeles',
            zipCode: '90002',
            description: 'Updated description.',
            serviceIds: ['SERVICE3'],
            status: true,
        });
        StaffService.deleteStaff.mockResolvedValue();
    };

    describe('Create Staff', () => {
        test('should create a new staff member successfully', async () => {
            req.body = {
                fullName: 'John Doe',
                email: '<EMAIL>',
                phoneNumber: '1234567890',
                address: '123 Main St',
                country: 'USA',
                state: 'California',
                city: 'Los Angeles',
                zipCode: '90001',
                description: 'Staff member in charge of operations.',
                serviceIds: ['SERVICE1', 'SERVICE2'],
                status: true,
            };
            mockServiceMethods();

            await createStaff(req, res);

            expect(StaffService.createStaff).toHaveBeenCalledWith(req.body);
            expect(res.status).toHaveBeenCalledWith(201);
            expect(res.json).toHaveBeenCalledWith({
                success: true,
                staff: {
                    sid: 1,
                    fullName: 'John Doe',
                    email: '<EMAIL>',
                    phoneNumber: '1234567890',
                    address: '123 Main St',
                    country: 'USA',
                    state: 'California',
                    city: 'Los Angeles',
                    zipCode: '90001',
                    description: 'Staff member in charge of operations.',
                    serviceIds: ['SERVICE1', 'SERVICE2'],
                    status: true,
                },
                message: 'Staff created successfully',
            });
        });
    });

    describe('Get Staff', () => {
        test('should fetch a list of staff members', async () => {
            req.query = { page: '1', limit: '10' };
            mockServiceMethods();

            await getStaff(req, res);

            expect(StaffService.getStaff).toHaveBeenCalledWith(
                expect.anything(),
                expect.anything(),
                expect.anything(),
                1,
                10
            );
            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.json).toHaveBeenCalledWith({
                success: true,
                staff: [
                    {
                        sid: 1,
                        fullName: 'John Doe',
                        email: '<EMAIL>',
                        phoneNumber: '1234567890',
                        address: '123 Main St',
                        country: 'USA',
                        state: 'California',
                        city: 'Los Angeles',
                        zipCode: '90001',
                        description: 'Staff member in charge of operations.',
                        serviceIds: ['SERVICE1', 'SERVICE2'],
                        status: true,
                    },
                ],
                total: 1,
                page: 1,
                pages: 1,
                message: 'Staff fetched successfully',
            });
        });
    });

    describe('Get Staff By ID', () => {
        test('should return a staff member by ID', async () => {
            req.params = { staffId: '1' };
            mockServiceMethods();

            await getStaffById(req, res);

            expect(StaffService.getStaffById).toHaveBeenCalledWith('1');
            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.json).toHaveBeenCalledWith({
                success: true,
                staff: {
                    sid: 1,
                    fullName: 'John Doe',
                    email: '<EMAIL>',
                    phoneNumber: '1234567890',
                    address: '123 Main St',
                    country: 'USA',
                    state: 'California',
                    city: 'Los Angeles',
                    zipCode: '90001',
                    description: 'Staff member in charge of operations.',
                    serviceIds: ['SERVICE1', 'SERVICE2'],
                    status: true,
                },
                message: 'Staff fetched successfully',
            });
        });

        test('should return 404 if staff member not found', async () => {
            req.params = { staffId: '1' };
            StaffService.getStaffById.mockResolvedValue(null);

            await getStaffById(req, res);

            expect(res.status).toHaveBeenCalledWith(404);
            expect(res.json).toHaveBeenCalledWith({
                success: false,
                code: 404,
                errors: [{ field: 'staffId', message: 'Staff not found' }],
                message: expect.stringMatching(
                    /.*The requested resource could not be found. Please check the URL and try again.*/
                ),
            });
        });
    });

    describe('Update Staff', () => {
        test('should update an existing staff member', async () => {
            req.params = { staffId: '1' };
            req.body = {
                fullName: 'John Doe Updated',
                email: '<EMAIL>',
                phoneNumber: '0987654321',
                address: '456 Another St',
                country: 'USA',
                state: 'California',
                city: 'Los Angeles',
                zipCode: '90002',
                description: 'Updated description.',
                serviceIds: ['SERVICE3'],
                status: true,
            };
            mockServiceMethods();

            await updateStaff(req, res);

            expect(StaffService.updateStaff).toHaveBeenCalledWith(
                '1',
                req.body
            );
            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.json).toHaveBeenCalledWith({
                success: true,
                staff: {
                    sid: 1,
                    fullName: 'John Doe Updated',
                    email: '<EMAIL>',
                    phoneNumber: '0987654321',
                    address: '456 Another St',
                    country: 'USA',
                    state: 'California',
                    city: 'Los Angeles',
                    zipCode: '90002',
                    description: 'Updated description.',
                    serviceIds: ['SERVICE3'],
                    status: true,
                },
                message: 'Staff updated successfully',
            });
        });

        test('should return 404 if staff member not found for update', async () => {
            req.params = { staffId: '1' };
            StaffService.getStaffById.mockResolvedValue(null);

            await updateStaff(req, res);

            expect(res.status).toHaveBeenCalledWith(404);
            expect(res.json).toHaveBeenCalledWith({
                success: false,
                code: 404,
                errors: [
                    { field: 'staffId', message: 'Staff not found for update' },
                ],
                message: expect.stringMatching(
                    /.*The requested resource could not be found. Please check the URL and try again.*/
                ),
            });
        });
    });

    describe('Delete Staff', () => {
        test('should delete an existing staff member', async () => {
            req.params = { staffId: '1' };
            mockServiceMethods();

            await deleteStaff(req, res);

            expect(StaffService.deleteStaff).toHaveBeenCalledWith('1');
            expect(res.status).toHaveBeenCalledWith(200);
            expect(res.json).toHaveBeenCalledWith({
                success: true,
                message: 'Staff deleted successfully',
            });
        });

        test('should return 404 if staff member not found for deletion', async () => {
            req.params = { staffId: '1' };
            StaffService.getStaffById.mockResolvedValue(null);

            await deleteStaff(req, res);

            expect(res.status).toHaveBeenCalledWith(404);
            expect(res.json).toHaveBeenCalledWith({
                success: false,
                code: 404,
                errors: [{ field: 'staffId', message: 'Staff not found' }],
                message: expect.stringMatching(
                    /.*The requested resource could not be found. Please check the URL and try again.*/
                ),
            });
        });
    });
});
