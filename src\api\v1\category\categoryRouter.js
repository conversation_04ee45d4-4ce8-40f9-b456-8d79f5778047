const express = require('express');

const router = express.Router();

const CategoryController = require('./categoryController');

const { validateCategory } = require('./categoryMiddleware');

const fetchAuth = require('../../common/utils/communicator');

router.post(
    '/',
    validateCategory,
    fetchAuth.fetchAuthAdminDataMiddleware,
    CategoryController.createCategory
);

router.get('/', CategoryController.getCategory);

router.get('/categoryAndSub', CategoryController.getCategoryAndSubCategories);

router.get('/:categoryId', CategoryController.getCategoryById);

router.put(
    '/:categoryId',
    validateCategory,
    fetchAuth.fetchAuthAdminDataMiddleware,
    CategoryController.updateCategory
);

router.delete(
    '/:categoryId',
    fetchAuth.fetchAuthAdminDataMiddleware,
    CategoryController.deleteCategory
);

module.exports = router;
