const mongoose = require('mongoose');
const CountersSchema = new mongoose.Schema(
    {
        counterId: { type: String, required: true, unique: true },
        seq: { type: Number, required: true },
    },
    { timestamps: true }
);

CountersSchema.statics.getNextSequence = async function () {
    const ret = await this.findOneAndUpdate(
        { counterId: 'autoVal' },
        { $inc: { seq: 1 } },
        {
            new: true,
            upsert: true,
        }
    );

    return ret.seq;
};

// create model
const Counter = new mongoose.model('AvailableBookingCounters', CountersSchema);

module.exports = Counter;
