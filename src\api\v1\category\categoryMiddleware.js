const { body } = require('express-validator');

const validateCategory = [
    body('categoryName')
        .notEmpty()
        .withMessage('categoryName is required.')
        .isString()
        .withMessage('categoryName must be a string.')
        .trim(),

    body('categorySlug')
        .optional()
        .isString()
        .withMessage('categorySlug must be a string.')
        .trim(),

    body('categoryImage')
        .optional()
        .isString()
        .withMessage('categoryImage must be a string.'),

    body('isCertificateRequired')
        .optional()
        .isBoolean()
        .withMessage('isCertificateRequired must be a boolean.'),

    body('isFeatured')
        .optional()
        .isBoolean()
        .withMessage('isFeatured must be a boolean.'),
];

module.exports = {
    validateCategory,
};
