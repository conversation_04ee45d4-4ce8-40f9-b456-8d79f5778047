const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema(
    {
        categoryId: {
            type: String,
            required: true,
        },

        categoryName: {
            type: String,
            required: true,
            trim: true,
        },

        categorySlug: {
            type: String,
            trim: true,
        },

        categoryImage: {
            type: String,
            trim: true,
        },

        isFeatured: {
            type: Boolean,
            default: false,
        },

        isCertificateRequired: {
            type: Boolean,
            default: false,
        },

        createdBy: {
            type: String,
            required: true,
            default: 'AID_1',
        },
    },
    { timestamps: true }
);

const Category = mongoose.model('Category', categorySchema);

module.exports = Category;
