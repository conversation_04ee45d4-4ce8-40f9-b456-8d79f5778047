const mongoose = require('mongoose');

const subCategorySchema = new mongoose.Schema(
    {
        subCategoryId: {
            type: String,
            required: true,
        },

        subCategoryName: {
            type: String,
            required: true,
            trim: true,
        },

        subCategorySlug: {
            type: String,
            trim: true,
        },

        subCategoryImage: {
            type: String,
            trim: true,
        },

        categoryId: {
            type: String,
            required: true,
        },

        createdAt: {
            type: String,
            required: true,
            default: 'AID_1',
        },
    },
    { timestamps: true }
);

const SubCategory = mongoose.model('SubCategory', subCategorySchema);

module.exports = SubCategory;
