/* eslint-disable prettier/prettier */
/* eslint-disable no-undef */
const request = require('supertest');
const app = require('../../../src/app');
const { setupDB, teardownDB } = require('../../environment/setupTest');
const { SERVICE_URL } = require('../../urls');

beforeAll(async () => {
    await setupDB();
});

afterAll(async () => {
    await teardownDB();
});

describe('Service Information API Integration', () => {
    let serviceId;

    it('should create a new service', async () => {
        const serviceData = {
            serviceTitle: 'Test Service',
            categoryId: 'cat123',
            subCategoryId: 'subcat123',
            price: 50,
            duration: '1 hour',
            description: 'This is a test service description.',
            additionalServicesId: ['serviceItem1', 'serviceItem2'],
            serviceAvailableId: ['availableTimeSlot1', 'availableTimeSlot2'],
            locationId: ['location1', 'location2'],
            galleryId: ['galleryItem1', 'galleryItem2'],
            seoId: ['seoItem1', 'seoItem2'],
        };

        try {
            const response = await request(app)
                .post(SERVICE_URL)
                .send(serviceData)
                .set('Accept', 'application/json');

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
            expect(response.body.serviceInfo).toHaveProperty('serviceId');

            serviceId = response.body.serviceInfo.serviceId;
        } catch (error) {
            console.error('Error during service creation: ', error);
        }
    });

    it('should fetch the created service by ID', async () => {
        if (!serviceId) {
            console.error('Service ID is not available. Test cannot run.');
            return;
        }

        const response = await request(app)
            .get(`${SERVICE_URL}/${serviceId}`)
            .set('Accept', 'application/json');

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.serviceInfo).toHaveProperty(
            'serviceId',
            serviceId
        );
    });

    it('should update an existing service', async () => {
        if (!serviceId) {
            console.error('Service ID is not available. Test cannot run.');
            return;
        }

        const updatedServiceData = {
            serviceTitle: 'Updated Test Service',
            price: 60,
        };

        const response = await request(app)
            .put(`${SERVICE_URL}/${serviceId}`)
            .send(updatedServiceData)
            .set('Accept', 'application/json');

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.serviceInfo.serviceTitle).toBe(
            updatedServiceData.serviceTitle
        );
        expect(response.body.serviceInfo.price).toBe(updatedServiceData.price);
    });

    it('should delete the created service', async () => {
        if (!serviceId) {
            console.error('Service ID is not available. Test cannot run.');
            return;
        }

        const response = await request(app)
            .delete(`${SERVICE_URL}/${serviceId}`)
            .set('Accept', 'application/json');

        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
        expect(response.body.message).toBe(
            'ServiceInformation deleted successfully'
        );
    });
});
