const mongoose = require('mongoose');

// Review Response Schema (for provider responses)
const reviewResponseSchema = new mongoose.Schema(
    {
        responseId: {
            type: String,
            required: true,
        },
        providerId: {
            type: String,
            required: true,
        },
        responseText: {
            type: String,
            required: true,
            trim: true,
            maxlength: 1000,
        },
        responseDate: {
            type: Date,
            default: Date.now,
        },
        isEdited: {
            type: Boolean,
            default: false,
        },
        editedAt: {
            type: Date,
        },
    },
    { _id: false }
);

// Review Images Schema
const reviewImageSchema = new mongoose.Schema(
    {
        imageId: {
            type: String,
            required: true,
        },
        imageUrl: {
            type: String,
            required: true,
        },
        imageCaption: {
            type: String,
            trim: true,
            maxlength: 200,
        },
        uploadedAt: {
            type: Date,
            default: Date.now,
        },
    },
    { _id: false }
);

// Helpful Votes Schema
const helpfulVotesSchema = new mongoose.Schema(
    {
        userId: {
            type: String,
            required: true,
        },
        voteType: {
            type: String,
            enum: ['helpful', 'not_helpful'],
            required: true,
        },
        votedAt: {
            type: Date,
            default: Date.now,
        },
    },
    { _id: false }
);

// Main Review Schema
const reviewSchema = new mongoose.Schema(
    {
        reviewId: {
            type: String,
            required: true,
            unique: true,
        },
        
        // Core Review Data
        customerId: {
            type: String,
            required: true,
        },
        providerId: {
            type: String,
            required: true,
        },
        serviceId: {
            type: String,
            required: true,
        },
        bookingId: {
            type: String,
            required: true,
        },
        
        // Review Content
        rating: {
            type: Number,
            required: true,
            min: 1,
            max: 5,
        },
        title: {
            type: String,
            trim: true,
            maxlength: 100,
        },
        comment: {
            type: String,
            required: true,
            trim: true,
            maxlength: 2000,
        },
        
        // Review Images
        images: [reviewImageSchema],
        
        // Review Status
        status: {
            type: String,
            enum: ['pending', 'approved', 'rejected', 'hidden'],
            default: 'approved',
        },
        
        // Moderation
        moderationReason: {
            type: String,
            trim: true,
        },
        moderatedBy: {
            type: String,
        },
        moderatedAt: {
            type: Date,
        },
        
        // Provider Response
        providerResponse: reviewResponseSchema,
        
        // Helpful Votes
        helpfulVotes: [helpfulVotesSchema],
        helpfulCount: {
            type: Number,
            default: 0,
        },
        notHelpfulCount: {
            type: Number,
            default: 0,
        },
        
        // Review Metadata
        isVerifiedPurchase: {
            type: Boolean,
            default: true,
        },
        isEdited: {
            type: Boolean,
            default: false,
        },
        editedAt: {
            type: Date,
        },
        
        // Timestamps
        reviewDate: {
            type: Date,
            default: Date.now,
        },
        
        // Soft Delete
        isDeleted: {
            type: Boolean,
            default: false,
        },
        deletedAt: {
            type: Date,
        },
        deletedBy: {
            type: String,
        },
    },
    { 
        timestamps: true,
        toJSON: { virtuals: true },
        toObject: { virtuals: true }
    }
);

// Indexes for better query performance
reviewSchema.index({ providerId: 1, status: 1, isDeleted: 1 });
reviewSchema.index({ serviceId: 1, status: 1, isDeleted: 1 });
reviewSchema.index({ customerId: 1, isDeleted: 1 });
reviewSchema.index({ bookingId: 1 });
reviewSchema.index({ rating: 1, status: 1, isDeleted: 1 });
reviewSchema.index({ reviewDate: -1 });

// Virtual for overall helpfulness score
reviewSchema.virtual('helpfulnessScore').get(function() {
    const total = this.helpfulCount + this.notHelpfulCount;
    if (total === 0) return 0;
    return (this.helpfulCount / total) * 100;
});

// Pre-save middleware to update helpful counts
reviewSchema.pre('save', function(next) {
    if (this.isModified('helpfulVotes')) {
        this.helpfulCount = this.helpfulVotes.filter(vote => vote.voteType === 'helpful').length;
        this.notHelpfulCount = this.helpfulVotes.filter(vote => vote.voteType === 'not_helpful').length;
    }
    next();
});

const Review = mongoose.model('Review', reviewSchema);

module.exports = Review;
