const Category = require('./categoryModel');
const Counter = require('./categoryCounter');
const SubCategory = require('./../subCategory/subCategoryModel');
const logger = require('../../common/utils/logger');

const createCategory = async (categoryData, createdBy) => {
    try {
        const cid = await Counter.getNextSequence();

        const categoryId = `CID_${cid}`;

        const category = new Category({
            ...categoryData,
            categoryId,
            createdBy,
        });

        return await category.save();
    } catch (error) {
        throw new Error('Error creating Category: ' + error.message);
    }
};

const getCategories = async (
    query,
    sortBy,
    sortDirection,
    pageNum,
    limitNum
) => {
    const skip = (pageNum - 1) * limitNum;

    if (![1, -1].includes(sortDirection)) {
        throw new Error(
            'Invalid sort direction. Use 1 for ascending and -1 for descending.'
        );
    }

    try {
        const categories = await Category.find(query)
            .sort({ updatedAt: -1, [sortBy]: sortDirection })
            .skip(skip)
            .limit(limitNum);

        const total = await Category.countDocuments(query);

        return { categories, total };
    } catch (error) {
        logger.error('Error fetching categories:', error);
        throw new Error('Failed to retrieve categories.');
    }
};

const getCategoriesAndSub = async () => {
    try {
        const categories = await Category.find({});

        const categoriesWithSubCategories = await Promise.all(
            categories.map(async (category) => {
                const subCategories = await SubCategory.find({
                    categoryId: category.categoryId,
                });
                return {
                    ...category.toObject(), // Convert category document to plain object
                    subCategories, // Add subcategories to the category object
                };
            })
        );
        return { categoriesWithSubCategories };
    } catch (error) {
        logger.error('Error fetching categories:', error);
        throw new Error('Failed to retrieve categories.');
    }
};

const getCategoryById = async (categoryId) => {
    return Category.findOne({ categoryId });
};

const updateCategory = async (categoryId, updateData) => {
    try {
        if (!categoryId) throw new Error('Category ID is required');

        const category = await getCategoryById(categoryId);

        if (!category) throw new Error('Category not found');

        const updatedCategory = await Category.findOneAndUpdate(
            { categoryId },
            { $set: updateData },
            { new: true, runValidators: true }
        );

        return updatedCategory;
    } catch (error) {
        logger.error('Error updating Category:', error.message);
        throw error;
    }
};

const deleteCategory = async (categoryId) => {
    try {
        if (!categoryId) throw new Error('Category ID is required');

        const category = await getCategoryById(categoryId);

        if (!category) throw new Error('Category not found');

        const deletedCategory = await Category.findOneAndDelete({
            categoryId: categoryId,
        });

        if (!deletedCategory) {
            throw new Error('Category not found');
        }

        return deletedCategory;
    } catch (error) {
        throw new Error(error.message);
    }
};

module.exports = {
    createCategory,
    getCategories,
    getCategoriesAndSub,
    getCategoryById,
    updateCategory,
    deleteCategory,
};
