const { validationResult } = require('express-validator');

const { InsufficientScopeError } = require('express-oauth2-jwt-bearer');

const SubCategoryService = require('./subCategoryService');

const {
    buildQueryFilter,
    validateAndParseParams,
} = require('../../common/utils/filter');

const logger = require('../../common/utils/logger');

const errorUtil = require('../../common/utils/error');

const createSubCategory = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            const formattedErrors = errors.array().map((err) => ({
                field: err.path,
                message: err.msg,
            }));

            const errorId = errorUtil.generateErrorId(); // Generate errorId
            const errorResponse = errorUtil.createErrorResponse(
                formattedErrors,
                errorUtil.ERROR_TYPES.VALIDATION_ERROR,
                422,
                errorId
            );
            logger.error(`Validation error for request: ${formattedErrors}`, {
                errorId,
                formattedErrors,
            });
            return res.status(422).json(errorResponse);
        }
        const userId = req.userData.user.userId;

        const subCategory = await SubCategoryService.createSubCategory(
            req.body,
            userId
        );
        logger.info(
            `SubCategory created with ID: ${subCategory.subCategoryId}`
        );

        return res.status(201).json({
            success: true,
            message: 'SubCategory created successfully',
            subCategory,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId(); // Generate errorId for this block
        if (error instanceof InsufficientScopeError) {
            logger.error(`Insufficient scope error: ${error.message}`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'scope', message: error.message }],
                errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
                403,
                errorId
            );
            return res.status(403).json(errorResponse);
        }

        logger.error(`Error creating subCategory: ${error.message}`, {
            errorId,
        });
        const errorResponse = errorUtil.createErrorResponse(
            [{ field: 'subCategory', message: error.message }],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getSubCategory = async (req, res) => {
    try {
        const {
            page = '1',
            limit = '100',
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
        } = req.query;

        const { pageNum, limitNum, sortDirection } = validateAndParseParams(
            page,
            limit,
            sortOrder
        );
        const query = buildQueryFilter(search);

        const { subCategories, total } =
            await SubCategoryService.getSubCategories(
                query,
                sortBy,
                sortDirection,
                pageNum,
                limitNum
            );

        const totalPages = limitNum > 0 ? Math.ceil(total / limitNum) : 0;
        logger.info(
            `Fetched ${subCategories.length} subCategories from page ${pageNum}`
        );

        return res.status(200).json({
            success: true,
            message: 'SubCategories fetched successfully',
            subCategories,
            total,
            page: pageNum,
            pages: totalPages,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId(); // Generate errorId for this block
        logger.error(`Error fetching subCategories: ${error.message}`, {
            errorId,
        });
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const getSubCategoryById = async (req, res) => {
    try {
        const { subCategoryId } = req.params;
        const subCategory =
            await SubCategoryService.getSubCategoryById(subCategoryId);

        if (!subCategory) {
            const errorId = errorUtil.generateErrorId(); // Generate errorId for this block
            logger.warn(`SubCategory with ID ${subCategoryId} not found`, {
                errorId,
            });
            const errorResponse = errorUtil.createErrorResponse(
                [{ field: 'subCategoryId', message: 'SubCategory not found' }],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        logger.info(`Fetched subCategory with ID: ${subCategoryId}`);
        return res.status(200).json({
            success: true,
            message: 'SubCategory fetched successfully',
            subCategory,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId(); // Generate errorId for this block
        logger.error(
            `Error fetching subCategory with ID ${req.params.subCategoryId}: ${error.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const updateSubCategory = async (req, res) => {
    try {
        const { subCategoryId } = req.params;
        const existingSubCategory =
            await SubCategoryService.getSubCategoryById(subCategoryId);

        if (!existingSubCategory) {
            const errorId = errorUtil.generateErrorId(); // Generate errorId for this block
            logger.warn(
                `SubCategory with ID ${subCategoryId} not found for update`,
                { errorId }
            );
            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'subCategoryId',
                        message: 'SubCategory not found for update',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        const updatedSubCategory = await SubCategoryService.updateSubCategory(
            subCategoryId,
            req.body
        );
        logger.info(
            `SubCategory with ID ${subCategoryId} updated successfully`
        );

        return res.status(200).json({
            success: true,
            message: 'SubCategory updated successfully',
            subCategory: updatedSubCategory,
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId(); // Generate errorId for this block
        logger.error(
            `Error updating subCategory with ID ${req.params.subCategoryId}: ${error.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

const deleteSubCategory = async (req, res) => {
    try {
        const { subCategoryId } = req.params;
        const subCategory =
            await SubCategoryService.getSubCategoryById(subCategoryId);

        if (!subCategory) {
            const errorId = errorUtil.generateErrorId(); // Generate errorId for this block
            logger.warn(
                `SubCategory with ID ${subCategoryId} not found for deletion`,
                { errorId }
            );
            const errorResponse = errorUtil.createErrorResponse(
                [
                    {
                        field: 'subCategoryId',
                        message: 'SubCategory not found for deletion',
                    },
                ],
                errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
                404,
                errorId
            );
            return res.status(404).json(errorResponse);
        }

        await SubCategoryService.deleteSubCategory(subCategoryId);
        logger.info(
            `SubCategory with ID ${subCategoryId} deleted successfully`
        );

        return res.status(200).json({
            success: true,
            message: 'SubCategory deleted successfully',
        });
    } catch (error) {
        const errorId = errorUtil.generateErrorId(); // Generate errorId for this block
        logger.error(
            `Error deleting subCategory with ID ${req.params.subCategoryId}: ${error.message}`,
            { errorId }
        );
        const errorResponse = errorUtil.createErrorResponse(
            [],
            errorUtil.ERROR_TYPES.INTERNAL_SERVER_ERROR,
            500,
            errorId
        );
        return res.status(500).json(errorResponse);
    }
};

module.exports = {
    createSubCategory,
    getSubCategory,
    getSubCategoryById,
    updateSubCategory,
    deleteSubCategory,
};
